-- 为接口配置表添加新的加密架构字段
-- 创建时间：2024-07-15
-- 说明：添加配置化加密服务架构所需的字段

-- 添加新的加密架构字段
ALTER TABLE `t_interface_config`
ADD COLUMN `encrypt_type` varchar(20) DEFAULT NULL COMMENT '加密类型：SERVICE(使用加密服务)、CLASS_METHOD(使用类方法)、BUILTIN(内置加密)' AFTER `signature_mode`,
ADD COLUMN `encrypt_service_name` varchar(100) DEFAULT NULL COMMENT '加密服务名称' AFTER `encrypt_type`,
ADD COLUMN `encrypt_class_name` varchar(200) DEFAULT NULL COMMENT '加密类名' AFTER `encrypt_service_name`,
ADD COLUMN `encrypt_method_name` varchar(50) DEFAULT NULL COMMENT '加密方法名' AFTER `encrypt_class_name`,
ADD COLUMN `decrypt_method_name` varchar(50) DEFAULT NULL COMMENT '解密方法名' AFTER `encrypt_method_name`,
ADD COLUMN `encrypt_service_url` varchar(500) DEFAULT NULL COMMENT '第三方加密服务URL' AFTER `decrypt_method_name`,
ADD COLUMN `encrypt_config` text DEFAULT NULL COMMENT '加密配置参数(JSON格式)' AFTER `encrypt_service_url`,
ADD COLUMN `namespace` varchar(200) DEFAULT NULL COMMENT 'WebService命名空间' AFTER `web_service_operation`,
ADD COLUMN `operation` varchar(100) DEFAULT NULL COMMENT '操作名称' AFTER `namespace`;

-- 添加索引
CREATE INDEX idx_encrypt_type ON `t_interface_config`(`encrypt_type`);
CREATE INDEX idx_encrypt_service_name ON `t_interface_config`(`encrypt_service_name`); 