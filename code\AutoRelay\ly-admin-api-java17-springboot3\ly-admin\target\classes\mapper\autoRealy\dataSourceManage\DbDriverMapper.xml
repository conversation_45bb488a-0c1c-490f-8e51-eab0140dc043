<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.dataSourceManage.dao.DbDriverDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.dataSourceManage.domain.DbDriver">
        <id column="id" property="id" />
        <result column="db_type" property="dbType" />
        <result column="driver_class_name" property="driverClassName" />
        <result column="driver_name" property="driverName" />
        <result column="driver_version" property="driverVersion" />
        <result column="driver_file_name" property="driverFileName" />
        <result column="driver_file_path" property="driverFilePath" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="update_user_id" property="updateUserId" />
    </resultMap>

    <!-- 查询数据库驱动列表 -->
    <select id="queryList" resultMap="BaseResultMap">
        SELECT *
        FROM t_auto_relay_db_driver
        <where>
            <if test="dbType != null and dbType != ''">
                AND db_type = #{dbType}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有数据库驱动 -->
    <select id="queryAll" resultMap="BaseResultMap">
        SELECT *
        FROM t_auto_relay_db_driver
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询数据库驱动 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT *
        FROM t_auto_relay_db_driver
        WHERE id = #{id}
    </select>

    <!-- 新增数据库驱动 -->
    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_auto_relay_db_driver (
            db_type,driver_name, driver_class_name, driver_version, driver_file_name, driver_file_path, description,
            create_time, update_time, create_user_id, update_user_id
        ) VALUES (
            #{dbType},#{driverName}, #{driverClassName}, #{driverVersion}, #{driverFileName}, #{driverFilePath}, #{description},
            #{createTime}, #{updateTime}, #{createUserId}, #{updateUserId}
        )
    </insert>

    <!-- 更新数据库驱动 -->
    <update id="update">
        UPDATE t_auto_relay_db_driver
        SET db_type = #{dbType},
            driver_name = #{driverName},
            driver_class_name = #{driverClassName},
            driver_version = #{driverVersion},
            driver_file_name = #{driverFileName},
            driver_file_path = #{driverFilePath},
            description = #{description},
            update_time = #{updateTime},
            update_user_id = #{updateUserId}
        WHERE id = #{id}
    </update>

    <!-- 删除数据库驱动 -->
    <delete id="delete">
        DELETE FROM t_auto_relay_db_driver
        WHERE id = #{id}
    </delete>
</mapper>
