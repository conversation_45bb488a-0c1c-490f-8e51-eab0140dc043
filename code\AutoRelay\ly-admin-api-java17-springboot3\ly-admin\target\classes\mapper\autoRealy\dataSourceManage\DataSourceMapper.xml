<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.dataSourceManage.dao.DataSourceDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.dataSourceManage.domain.AutoDataSource">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="db_type" property="dbType" />
        <result column="driver_id" property="driverId" />
        <result column="host" property="host" />
        <result column="port" property="port" />
        <result column="database_name" property="databaseName" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="url" property="url" />
        <result column="max_active" property="maxActive" />
        <result column="initial_size" property="initialSize" />
        <result column="min_idle" property="minIdle" />
        <result column="max_wait" property="maxWait" />
        <result column="connection_status" property="connectionStatus" />
        <result column="is_default" property="isDefault" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="update_user_id" property="updateUserId" />
    </resultMap>

    <!-- 分页查询数据源列表 -->
    <select id="queryList" resultMap="BaseResultMap">
        SELECT *
        FROM t_auto_relay_data_source
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="dbType != null and dbType != ''">
                AND db_type = #{dbType}
            </if>
            <if test="connectionStatus != null">
                AND connection_status = #{connectionStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有数据源 -->
    <select id="queryAll" resultMap="BaseResultMap">
        SELECT *
        FROM t_auto_relay_data_source
        ORDER BY is_default DESC, create_time DESC
    </select>

    <!-- 根据ID查询数据源 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT *
        FROM t_auto_relay_data_source
        WHERE id = #{id}
    </select>

    <!-- 新增数据源 -->
    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_auto_relay_data_source (
            name, description, db_type, driver_id, host, port, database_name, username, password, url,
            max_active, initial_size, min_idle, max_wait, connection_status, is_default,
            create_time, update_time, create_user_id, update_user_id
        ) VALUES (
            #{name}, #{description}, #{dbType}, #{driverId}, #{host}, #{port}, #{databaseName}, #{username}, #{password}, #{url},
            #{maxActive}, #{initialSize}, #{minIdle}, #{maxWait}, #{connectionStatus}, #{isDefault},
            #{createTime}, #{updateTime}, #{createUserId}, #{updateUserId}
        )
    </insert>

    <!-- 更新数据源 -->
    <update id="update">
        UPDATE t_auto_relay_data_source
        SET name = #{name},
            description = #{description},
            db_type = #{dbType},
            driver_id = #{driverId},
            host = #{host},
            port = #{port},
            database_name = #{databaseName},
            username = #{username},
            password = #{password},
            url = #{url},
            max_active = #{maxActive},
            initial_size = #{initialSize},
            min_idle = #{minIdle},
            max_wait = #{maxWait},
            connection_status = #{connectionStatus},
            update_time = #{updateTime},
            update_user_id = #{updateUserId}
        WHERE id = #{id}
    </update>

    <!-- 删除数据源 -->
    <delete id="delete">
        DELETE FROM t_auto_relay_data_source
        WHERE id = #{id}
    </delete>

    <!-- 更新数据源连接状态 -->
    <update id="updateConnectionStatus">
        UPDATE t_auto_relay_data_source
        SET connection_status = #{connectionStatus},
            update_time = #{updateTime},
            update_user_id = #{updateUserId}
        WHERE id = #{id}
    </update>

    <!-- 设置默认数据源 -->
    <update id="setDefault">
        UPDATE t_auto_relay_data_source
        SET is_default = 0
        WHERE is_default = 1;

        UPDATE t_auto_relay_data_source
        SET is_default = 1,
            update_time = #{updateTime},
            update_user_id = #{updateUserId}
        WHERE id = #{id}
    </update>

    <!-- 清除默认数据源状态 -->
    <update id="clearDefaultStatus">
        UPDATE t_auto_relay_data_source
        SET is_default = 0
        WHERE is_default = 1
    </update>
</mapper>
