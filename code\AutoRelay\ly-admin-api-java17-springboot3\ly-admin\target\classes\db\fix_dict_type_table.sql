-- 修复字典类型表结构
-- 为已存在的t_data_dict_type表添加sort_order字段
-- 
-- @Author:    贺哥
-- @Date:      2024-12-28
-- @Copyright  <a href="https://lingyuelabs.cn">凌跃</a>

-- 1. 为现有的t_data_dict_type表添加sort_order字段
ALTER TABLE t_data_dict_type 
ADD COLUMN IF NOT EXISTS sort_order INT(11) DEFAULT 0 COMMENT '排序' 
AFTER remark;

-- 2. 为现有数据设置排序值
UPDATE t_data_dict_type 
SET sort_order = 
    CASE 
        WHEN dict_type_code = 'CERTIFICATE_TYPE' THEN 1
        WHEN dict_type_code = 'GENDER' THEN 2
        WHEN dict_type_code = 'EDUCATION' THEN 3
        WHEN dict_type_code = 'MARITAL_STATUS' THEN 4
        WHEN dict_type_code = 'NATIONALITY' THEN 5
        ELSE 999
    END 
WHERE sort_order = 0;

-- 3. 检查表结构是否正确
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 't_data_dict_type' 
ORDER BY ORDINAL_POSITION; 