<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.dataDictMapping.dao.DataDictTypeDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.dataDictMapping.domain.DataDictTypeEntity">
        <id column="id" property="id" />
        <result column="system_code" property="systemCode" />
        <result column="dict_type_code" property="dictTypeCode" />
        <result column="dict_type_name" property="dictTypeName" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="sort_order" property="sortOrder" />
        <result column="deleted_flag" property="deletedFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, system_code, dict_type_code, dict_type_name, description, status, 
        remark, sort_order, deleted_flag, create_time, update_time
    </sql>

    <!-- 根据系统编码查询字典类型列表 -->
    <select id="selectBySystemCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_dict_type
        WHERE deleted_flag = 0
        AND system_code = #{systemCode}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据系统编码和字典类型编码查询字典类型 -->
    <select id="selectBySystemAndDictType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_dict_type
        WHERE deleted_flag = 0
        AND system_code = #{systemCode}
        AND dict_type_code = #{dictTypeCode}
        LIMIT 1
    </select>

    <!-- 检查字典类型编码是否存在 -->
    <select id="checkDictTypeCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM t_data_dict_type
        WHERE deleted_flag = 0
        AND system_code = #{systemCode}
        AND dict_type_code = #{dictTypeCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper> 