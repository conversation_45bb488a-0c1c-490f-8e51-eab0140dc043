<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.interfaceConfig.dao.InterfaceConfigDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.InterfaceConfigEntity">
        <id column="id" property="id" />
        <result column="system_code" property="systemCode" />
        <result column="system_name" property="systemName" />
        <result column="interface_name" property="interfaceName" />
        <result column="interface_type" property="interfaceType" />
        <result column="request_url" property="requestUrl" />
        <result column="request_method" property="requestMethod" />
        <result column="web_service_operation" property="webServiceOperation" />
        <result column="namespace" property="namespace" />
        <result column="operation" property="operation" />
        <result column="param_mapping_id" property="paramMappingId" />
        <result column="request_body_type" property="requestBodyType" />
        <result column="request_body_template" property="requestBodyTemplate" />
        <result column="input_data_template" property="inputDataTemplate" />
        <result column="enable_encryption" property="enableEncryption" />
        <result column="encryption_algorithm" property="encryptionAlgorithm" />
        <result column="encryption_key" property="encryptionKey" />
        <result column="encryption_mode" property="encryptionMode" />
        <result column="encryption_iv" property="encryptionIv" />
        <result column="encryption_padding" property="encryptionPadding" />
        <result column="enable_signature" property="enableSignature" />
        <result column="signature_algorithm" property="signatureAlgorithm" />
        <result column="signature_key" property="signatureKey" />
        <result column="signature_mode" property="signatureMode" />
        <result column="encrypt_type" property="encryptType" />
        <result column="encrypt_service_name" property="encryptServiceName" />
        <result column="encrypt_class_name" property="encryptClassName" />
        <result column="encrypt_method_name" property="encryptMethodName" />
        <result column="decrypt_method_name" property="decryptMethodName" />
        <result column="encrypt_service_url" property="encryptServiceUrl" />
        <result column="encrypt_config" property="encryptConfig" />
        <result column="http_encrypt_data_param" property="httpEncryptDataParam" />
        <result column="http_encrypt_result_path" property="httpEncryptResultPath" />
        <result column="http_signature_result_path" property="httpSignatureResultPath" />
        <result column="http_access_token_path" property="httpAccessTokenPath" />
        <result column="http_extra_params" property="httpExtraParams" />
        <result column="http_headers" property="httpHeaders" />
        <result column="http_timeout" property="httpTimeout" />
        <result column="http_retry_count" property="httpRetryCount" />
        <result column="data_source_id" property="dataSourceId" />
        <result column="table_name" property="tableName" />
        <result column="sql_query" property="sqlQuery" />
        <result column="max_rows" property="maxRows" />
        <result column="status" property="status" />
        <result column="response_processor_class" property="responseProcessorClass" />
        <result column="response_parse_config" property="responseParseConfig" />
        <result column="enable_response_parse" property="enableResponseParse" />
        <result column="description" property="description" />
        <result column="deleted_flag" property="deletedFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, system_code, system_name, interface_name, interface_type, request_url, request_method,
        web_service_operation, namespace, operation, param_mapping_id, request_body_type, request_body_template, 
        input_data_template, enable_encryption, encryption_algorithm, encryption_key, encryption_mode, encryption_iv, 
        encryption_padding, enable_signature, signature_algorithm, signature_key, signature_mode, encrypt_type,
        encrypt_service_name, encrypt_class_name, encrypt_method_name, decrypt_method_name, encrypt_service_url,
        encrypt_config, http_encrypt_data_param, http_encrypt_result_path, http_signature_result_path, 
        http_access_token_path, http_extra_params, http_headers, http_timeout, http_retry_count, 
        data_source_id, table_name, sql_query, max_rows, response_processor_class, response_parse_config, enable_response_parse, status,
        description, deleted_flag, create_time, update_time
    </sql>

    <!-- 根据ID查询接口配置 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_interface_config
        WHERE id = #{id}
        AND deleted_flag = 0
    </select>

    <!-- 根据系统编码和接口名称查询接口配置 -->
    <select id="selectBySystemCodeAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_interface_config
        WHERE system_code = #{systemCode}
        AND interface_name = #{interfaceName}
        AND deleted_flag = 0
    </select>

    <!-- 分页查询接口配置 -->
    <select id="queryPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_interface_config
        <where>
            deleted_flag = 0
            <if test="systemCode != null and systemCode != ''">
                AND system_code = #{systemCode}
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                AND interface_name LIKE CONCAT('%', #{interfaceName}, '%')
            </if>
            <if test="interfaceType != null and interfaceType != ''">
                AND interface_type = #{interfaceType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

    <!-- 批量删除接口配置（逻辑删除） -->
    <update id="batchDelete">
        UPDATE t_interface_config
        SET deleted_flag = 1,
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_flag = 0
    </update>

    <!-- 更新接口状态 -->
    <update id="updateStatus">
        UPDATE t_interface_config
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id}
        AND deleted_flag = 0
    </update>
</mapper>
