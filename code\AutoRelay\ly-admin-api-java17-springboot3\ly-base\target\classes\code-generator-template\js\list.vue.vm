<!--
  * ${basic.description}
  *
  * @Author:    ${basic.frontAuthor}
  * @Date:      ${basic.frontDate}
  * @Copyright  ${basic.copyright}
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
        #foreach ($field in $queryFields)
            #if($field.queryTypeEnum == "Like")
              <a-form-item label="${field.label}" class="smart-query-form-item">
                <a-input style="width: ${field.width}" v-model:value="queryForm.${field.fieldName}"
                         placeholder="${field.label}"/>
              </a-form-item>
            #end
            #if($field.queryTypeEnum == "Equal")
              <a-form-item label="${field.label}" class="smart-query-form-item">
                <a-input style="width: ${field.width}" v-model:value="queryForm.${field.fieldName}"
                         placeholder="${field.label}"/>
              </a-form-item>
            #end
            #if($field.queryTypeEnum == "Dict")
              <a-form-item label="${field.label}" class="smart-query-form-item">
                <DictSelect keyCode="$!{field.dict}" placeholder="${field.label}"
                            v-model:value="queryForm.${field.fieldName}" width="${field.width}"/>
              </a-form-item>
            #end
            #if($field.queryTypeEnum == "Enum")
              <a-form-item label="$codeGeneratorTool.removeEnumDesc(${field.label})" class="smart-query-form-item">
                <SmartEnumSelect width="${field.width}" v-model:value="queryForm.${field.fieldName}"
                                 enumName="$!{field.frontEnumName}"
                                 placeholder="$codeGeneratorTool.removeEnumDesc(${field.label})"/>
              </a-form-item>
            #end
            #if($field.queryTypeEnum == "Date")
              <a-form-item label="${field.label}" class="smart-query-form-item">
                <a-date-picker valueFormat="YYYY-MM-DD" v-model:value="queryForm.$!{field.fieldName}"
                               style="width: ${field.width}"/>
              </a-form-item>
            #end
            #if($field.queryTypeEnum == "DateRange")
              <a-form-item label="${field.label}" class="smart-query-form-item">
                <a-range-picker v-model:value="queryForm.$!{field.fieldName}" :presets="defaultTimeRanges"
                                style="width: ${field.width}"
                                @change="onChange$codeGeneratorTool.lowerCamel2UpperCamel(${field.fieldName})"/>
              </a-form-item>
            #end
        #end
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
          #if($insertAndUpdate.isSupportInsertAndUpdate)
            <a-button @click="showForm" type="primary" size="small">
              <template #icon>
                <PlusOutlined/>
              </template>
              新建
            </a-button>
          #end
          #if($deleteInfo.isSupportDelete && ($deleteInfo.deleteEnum == "Batch"||$deleteInfo.deleteEnum ==
              "SingleAndBatch"))
            <a-button @click="confirmBatchDelete" type="primary" danger size="small"
                      :disabled="selectedRowKeyList.length == 0">
              <template #icon>
                <DeleteOutlined/>
              </template>
              批量删除
            </a-button>
          #end
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData"/>
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="$!{primaryKeyFieldName}"
        bordered
        :loading="tableLoading"
        :pagination="false"
        #if($deleteInfo.isSupportDelete && ($deleteInfo.deleteEnum == "Batch"||$deleteInfo.deleteEnum ==
            "SingleAndBatch"))
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
        #end
    >
      <template #bodyCell="{ text, record, column }">

        <!-- 有图片预览时 注释解开并把下面的'picture'修改成自己的图片字段名即可 -->
        <!-- <template v-if="column.dataIndex === 'picture'">
            <FilePreview :fileList="text" type="picture" />
          </template> -->

        <!-- 使用字典时 注释解开并把下面的'dict'修改成自己的字典字段名即可 有多个字典字段就复制多份同理修改 不然不显示字典 -->
        <!-- 方便修改tag的颜色 orange green purple success processing error default warning -->
        <!-- <template v-if="column.dataIndex === 'dict'">
          <a-tag color="cyan">
            {{ text && text.length > 0 ? text.map((e) => e.valueName).join(',') : '暂无' }}
          </a-tag>
        </template> -->

        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
              #if($insertAndUpdate.isSupportInsertAndUpdate)
                <a-button @click="showForm(record)" type="link">编辑</a-button>
              #end
              #if($deleteInfo.isSupportDelete && ($deleteInfo.deleteEnum == "Single"||$deleteInfo.deleteEnum ==
                  "SingleAndBatch"))
                <a-button @click="onDelete(record)" danger type="link">删除</a-button>
              #end
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <$!{name.upperCamel}Form ref="formRef" @reloadList="queryData"/>

  </a-card>
</template>
<script setup>
  import {onMounted, reactive, ref} from 'vue';
  import {message, Modal} from 'ant-design-vue';
  import {SmartLoading} from '/@/components/framework/smart-loading';

  import {
          $!{name.lowerCamel}Api
  }
    from
        '/@/api/business/';
  import {smartSentry} from '/@/lib/smart-sentry';
      #foreach ($import in $frontImportList)
          $!{import}
      #end
  //import FilePreview from '/@/components/support/file-preview/index.vue'; // 图片预览组件

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
      #foreach ($field in $tableFields)
          #if($field.showFlag)
            {
              title: '$!{field.label}',
              dataIndex: '$!{field.fieldName}',
              ellipsis: $!{field.ellipsisFlag},
                #if(${field.width} > 0)
                  width: $!{field.width},
                #end
            },
          #end
      #end
      #if($insertAndUpdate.isSupportInsertAndUpdate || $insertAndUpdate.isSupportInsertAndUpdate)
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 90,
        },
      #end
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
      #foreach ($field in $queryFields)
          #if($field.queryTypeEnum == "DateRange")
                  $!{field.fieldName}: [], //$!{field.label}
                  $!{field.fieldName}Begin: undefined, //$!{field.label} 开始
                  $!{field.fieldName}End: undefined, //$!{field.label} 结束
          #end
          #if($field.queryTypeEnum != "DateRange")
                  $!{field.fieldName}: undefined, //$!{field.label}
          #end
      #end
    pageNum: 1,
    pageSize: 10,
  };
  // 查询表单form
  const queryForm = reactive({...queryFormState});
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await $!{name.lowerCamel}Api.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

      #foreach ($field in $queryFields)
          #if($field.queryTypeEnum == "DateRange")
          function onChange$codeGeneratorTool.lowerCamel2UpperCamel(${field.fieldName})(dates, dateStrings) {
            queryForm.$!{field.fieldName}Begin = dateStrings[0];
            queryForm.$!{field.fieldName}End = dateStrings[1];
          }

          #end
      #end

  onMounted(queryData);

      #if($insertAndUpdate.isSupportInsertAndUpdate)
      // ---------------------------- 添加/修改 ----------------------------
      const formRef = ref();

      function showForm(data) {
        formRef.value.show(data);
      }
      #end

      #if($deleteInfo.isSupportDelete)
          #if($deleteInfo.deleteEnum == "Batch" || $deleteInfo.deleteEnum == "SingleAndBatch")
          // ---------------------------- 单个删除 ----------------------------
          //确认删除
          function onDelete(data) {
            Modal.confirm({
              title: '提示',
              content: '确定要删除选吗?',
              okText: '删除',
              okType: 'danger',
              onOk() {
                requestDelete(data);
              },
              cancelText: '取消',
              onCancel() {
              },
            });
          }

          //请求删除
          async function requestDelete(data) {
            SmartLoading.show();
            try {
              let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
              };
              await $!{name.lowerCamel}Api.delete(data.$!{primaryKeyFieldName});
              message.success('删除成功');
              queryData();
            } catch (e) {
              smartSentry.captureError(e);
            } finally {
              SmartLoading.hide();
            }
          }
          #end

          #if($deleteInfo.deleteEnum == "Single" || $deleteInfo.deleteEnum == "SingleAndBatch")
          // ---------------------------- 批量删除 ----------------------------

          // 选择表格行
          const selectedRowKeyList = ref([]);

          function onSelectChange(selectedRowKeys) {
            selectedRowKeyList.value = selectedRowKeys;
          }

          // 批量删除
          function confirmBatchDelete() {
            Modal.confirm({
              title: '提示',
              content: '确定要批量删除这些数据吗?',
              okText: '删除',
              okType: 'danger',
              onOk() {
                requestBatchDelete();
              },
              cancelText: '取消',
              onCancel() {
              },
            });
          }

          //请求批量删除
          async function requestBatchDelete() {
            try {
              SmartLoading.show();
              await $!{name.lowerCamel}Api.batchDelete(selectedRowKeyList.value);
              message.success('删除成功');
              queryData();
            } catch (e) {
              smartSentry.captureError(e);
            } finally {
              SmartLoading.hide();
            }
          }
          #end
      #end
</script>
