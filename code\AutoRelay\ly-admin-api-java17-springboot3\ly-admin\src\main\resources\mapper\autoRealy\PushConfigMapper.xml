<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.pushRecord.dao.PushConfigDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushConfigEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="data_id_field" property="dataIdField" jdbcType="VARCHAR"/>
        <result column="target_data_id_field" property="targetDataIdField" jdbcType="VARCHAR"/>
        <result column="enable_deduplication" property="enableDeduplication" jdbcType="BOOLEAN"/>
        <result column="enable_data_change_detection" property="enableDataChangeDetection" jdbcType="BOOLEAN"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="batch_size" property="batchSize" jdbcType="INTEGER"/>
        <result column="time_window_hours" property="timeWindowHours" jdbcType="INTEGER"/>

        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="update_time_field" property="updateTimeField" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, task_id, data_id_field, target_data_id_field, enable_deduplication, enable_data_change_detection,
        max_retry_count, batch_size, time_window_hours, start_time, update_time_field,
        create_time, update_time
    </sql>

    <!-- 根据任务ID查询推送配置 -->
    <select id="selectByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_push_config
        WHERE task_id = #{taskId,jdbcType=BIGINT}
    </select>

    <!-- 根据任务ID删除推送配置 -->
    <delete id="deleteByTaskId" parameterType="java.lang.Long">
        DELETE FROM t_push_config
        WHERE task_id = #{taskId,jdbcType=BIGINT}
    </delete>

</mapper>
