<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.pushRecord.dao.PushRecordDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushRecordEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
        <result column="data_id" property="dataId" jdbcType="VARCHAR"/>
        <result column="data_hash" property="dataHash" jdbcType="VARCHAR"/>
        <result column="push_status" property="pushStatus" jdbcType="TINYINT"/>
        <result column="push_time" property="pushTime" jdbcType="TIMESTAMP"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, batch_no, data_id, data_hash, push_status, push_time, retry_count, error_message, create_time, update_time
    </sql>

    <!-- 根据任务ID和数据ID查询推送记录 -->
    <select id="selectByTaskIdAndDataId" resultMap="BaseResultMap">
        SELECT * FROM t_push_record
        WHERE task_id = #{taskId} AND data_id = #{dataId}
        LIMIT 1
    </select>

    <!-- 批量插入推送记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_push_record (
            task_id, batch_no, data_id, data_hash, push_status, push_time, retry_count, error_message, create_time, update_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.taskId},
                #{record.batchNo},
                #{record.dataId},
                #{record.dataHash},
                #{record.pushStatus},
                #{record.pushTime},
                #{record.retryCount},
                #{record.errorMessage},
                #{record.createTime},
                #{record.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            batch_no = VALUES(batch_no),
            data_hash = VALUES(data_hash),
            push_status = VALUES(push_status),
            push_time = VALUES(push_time),
            retry_count = VALUES(retry_count),
            error_message = VALUES(error_message),
            update_time = VALUES(update_time)
    </insert>

    <!-- 查询待推送的记录 -->
    <select id="selectPendingRecords" resultMap="BaseResultMap">
        SELECT * FROM t_push_record
        WHERE task_id = #{taskId} AND push_status = 0
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 查询需要重试的记录 -->
    <select id="selectRetryRecords" resultMap="BaseResultMap">
        SELECT * FROM t_push_record
        WHERE task_id = #{taskId}
          AND push_status = 2
          AND retry_count &lt; #{maxRetryCount}
        ORDER BY update_time ASC
        LIMIT #{limit}
    </select>

    <!-- 更新推送状态 -->
    <update id="updatePushStatus">
        UPDATE t_push_record
        SET push_status = #{pushStatus},
            push_time = NOW(),
            error_message = #{errorMessage},
            update_time = NOW()
        WHERE id = #{id}
    </update>

  <!-- 只新增失败记录（存在则忽略，不做更新） -->
  <insert id="insertFailureIgnore">
    INSERT IGNORE INTO t_push_record (
      task_id, batch_no, data_id, data_hash, push_status, retry_count, error_message, push_time, create_time, update_time
    ) VALUES (
      #{record.taskId,jdbcType=BIGINT},
      #{record.batchNo,jdbcType=VARCHAR},
      #{record.dataId,jdbcType=VARCHAR},
      #{record.dataHash,jdbcType=VARCHAR},
      2,
      1,
      #{record.errorMessage,jdbcType=LONGVARCHAR},
      NOW(),
      NOW(),
      NOW()
    )
  </insert>

    <!-- 增加重试次数 -->
    <update id="incrementRetryCount">
        UPDATE t_push_record
        SET retry_count = retry_count + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据任务ID删除推送记录 -->
    <delete id="deleteByTaskId">
        DELETE FROM t_push_record WHERE task_id = #{taskId}
    </delete>

    <!-- 统计推送记录数量 -->
    <select id="countByTaskIdAndStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_push_record
        WHERE task_id = #{taskId}
        <if test="pushStatus != null">
            AND push_status = #{pushStatus}
        </if>
    </select>

    <!-- 根据批次号查询推送记录 -->
    <select id="selectByBatchNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_push_record
        WHERE batch_no = #{batchNo,jdbcType=VARCHAR}
        ORDER BY create_time ASC
    </select>

    <!-- 根据批次号获取最大data_id -->
    <select id="selectMaxDataIdByBatchNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(CAST(data_id AS UNSIGNED))
        FROM t_push_record
        WHERE batch_no = #{batchNo,jdbcType=VARCHAR}
        AND data_id REGEXP '^[0-9]+$'
    </select>

    <!-- 批量更新推送状态（根据data_id列表） -->
    <update id="batchUpdatePushStatusByDataIds">
        UPDATE t_push_record
        SET push_status = #{pushStatus,jdbcType=INTEGER},
            push_time = NOW(),
            error_message = #{errorMessage,jdbcType=LONGVARCHAR},
            update_time = NOW()
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        AND data_id IN
        <foreach collection="dataIds" item="dataId" open="(" separator="," close=")">
            #{dataId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!-- 批量增加重试次数（根据data_id列表） -->
    <update id="batchIncrementRetryCountByDataIds">
        UPDATE t_push_record
        SET retry_count = retry_count + 1,
            update_time = NOW()
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        AND data_id IN
        <foreach collection="dataIds" item="dataId" open="(" separator="," close=")">
            #{dataId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!-- 根据ID更新推送记录 -->
    <update id="updateById" parameterType="net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushRecordEntity">
        UPDATE t_push_record
        SET task_id = #{taskId,jdbcType=BIGINT},
            batch_no = #{batchNo,jdbcType=VARCHAR},
            data_id = #{dataId,jdbcType=VARCHAR},
            data_hash = #{dataHash,jdbcType=VARCHAR},
            push_status = #{pushStatus,jdbcType=TINYINT},
            push_time = #{pushTime,jdbcType=TIMESTAMP},
            retry_count = #{retryCount,jdbcType=INTEGER},
            error_message = #{errorMessage,jdbcType=LONGVARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 插入单条推送记录 -->
    <insert id="insert" parameterType="net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushRecordEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_push_record (
            task_id, batch_no, data_id, data_hash, push_status, push_time, retry_count, error_message, create_time, update_time
        ) VALUES (
            #{taskId,jdbcType=BIGINT},
            #{batchNo,jdbcType=VARCHAR},
            #{dataId,jdbcType=VARCHAR},
            #{dataHash,jdbcType=VARCHAR},
            #{pushStatus,jdbcType=TINYINT},
            #{pushTime,jdbcType=TIMESTAMP},
            #{retryCount,jdbcType=INTEGER},
            #{errorMessage,jdbcType=LONGVARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

</mapper>
