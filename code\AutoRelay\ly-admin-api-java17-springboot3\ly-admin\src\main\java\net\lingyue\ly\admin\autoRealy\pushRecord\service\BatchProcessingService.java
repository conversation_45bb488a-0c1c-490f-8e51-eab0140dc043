package net.lingyue.ly.admin.autoRealy.pushRecord.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.autoRealy.dataSourceManage.domain.AutoDataSource;
import net.lingyue.ly.admin.autoRealy.dataSourceManage.domain.DbDriver;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.InterfaceConfigEntity;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.entity.InterfaceTaskEntity;
import net.lingyue.ly.admin.autoRealy.pushRecord.dao.PushBatchDao;
import net.lingyue.ly.admin.autoRealy.pushRecord.dao.PushConfigDao;
import net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushBatchEntity;
import net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushConfigEntity;
import net.lingyue.ly.admin.util.SqlExecutorUtil;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.vo.SqlQueryResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 分批处理服务 - 基于现有t_push_batch表的优化版
 *
 * 实现真正的分批处理：
 * 1. 每次调用只处理一批数据（比如500条）
 * 2. 利用t_push_batch表的progress_info字段记录断点信息
 * 3. 配合定时任务调度，实现"每30秒500条"的需求
 *
 * 任务调度整体流程（修复版）：
 *
 * ### 配置参数
 * - 推送起始时间：数据查询的起点
 * - 批次大小：500条（示例）
 * - 时间间隔：避免大数据量查询的窗口大小
 *
 * ### 执行流程
 * 第一次执行：
 * 1. 根据推送起始时间 + 时间间隔计算时间窗口
 * 2. 查询窗口内数据（起始时间 → 窗口结束时间）
 * 3. 如果查询到500条（满批次）→ 说明窗口数据未完成
 * 4. 记录这批数据的最大时间和ID
 *
 * 第二次执行（30秒后）：
 * 1. 使用上次记录的最大时间和ID作为查询起点
 * 2. 继续查询同一窗口的剩余数据
 * 3. 重复直到查询无数据或数据量 < 500
 *
 * 窗口完成后：
 * 1. 重新计算下一个窗口
 * 2. 关键点：下一窗口的开始时间 = 上次记录的最大时间和ID（不是配置的推送起始时间）
 * 3. 如果下一窗口也无数据 → 说明已处理到最新数据
 * 4. 保持最大时间和ID不变，等待新数据
 *
 * @Author:    贺哥
 * @Date:      2024-12-19
 * @Copyright  <a href="https://lingyuelabs.cn">凌跃</a>
 */
@Slf4j
@Service
public class BatchProcessingService {

    @Resource
    private PushConfigDao pushConfigDao;

    @Resource
    private PushBatchDao pushBatchDao;

    @Resource
    private PushRecordService pushRecordService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 分批处理任务数据 - 每次只处理一批
     *
     * @param task 任务实体
     * @param dataSource 数据源
     * @param dbDriver 数据库驱动
     * @param processedSql 处理后的SQL
     * @return 批次处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchProcessingResult processBatchData(InterfaceTaskEntity task,
                                                  PushConfigEntity pushConfig,
                                                  AutoDataSource dataSource,
                                                  DbDriver dbDriver,
                                                  String processedSql) {

        Long taskId = task.getId();
        // 执行分批处理 - 每次只处理一批
        return processSingleBatchWithProgress(taskId, dataSource, dbDriver, processedSql, pushConfig);
    }

    /**
     * 处理单个批次（带进度记录）- 基于时间窗口优化
     *
     * 实现任务调度整体流程：
     * 1. 根据推送起始时间 + 时间间隔计算时间窗口
     * 2. 查询窗口内数据，如果满批次说明窗口数据未完成
     * 3. 记录这批数据的最大时间和ID作为断点
     * 4. 下次执行使用上次记录的最大时间和ID作为查询起点
     * 5. 窗口完成后，下一窗口的开始时间 = 上次记录的最大时间和ID
     */
    private BatchProcessingResult processSingleBatchWithProgress(Long taskId,
                                                               AutoDataSource dataSource,
                                                               DbDriver dbDriver,
                                                               String processedSql,
                                                               PushConfigEntity pushConfig) {

        BatchProcessingResult result = new BatchProcessingResult();

        try {
            // 获取上次处理的断点信息（包含时间窗口信息）
            BreakpointInfo breakpoint = getLastBreakpointFromHistory(taskId);

            // 计算当前时间窗口 - 关键修复：基于断点时间而非配置起始时间
            calculateCurrentTimeWindow(breakpoint, pushConfig);

            log.info("任务[{}]开始处理时间窗口: {} ~ {}, 上次断点: 时间={}, ID={}",
                    taskId, breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd(),
                    breakpoint.getLastTime(), breakpoint.getLastId());

            // 检查当前窗口是否已完成
            if (breakpoint.isWindowCompleted()) {
                // 窗口已完成，准备下一个窗口
                prepareNextTimeWindow(breakpoint, pushConfig);
                log.info("任务[{}]当前窗口已完成，切换到下一窗口: {} ~ {}",
                        taskId, breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd());

                // 保存新窗口信息到Redis
                saveBreakpointToRedis(taskId, breakpoint);
            }

            // 在当前时间窗口内处理数据
            int totalProcessedInWindow = 0;
            int totalSuccessInWindow = 0;
            String lastProcessedTime = breakpoint.getLastTime();
            String lastProcessedId = breakpoint.getLastId();

            // 创建新的批次记录
            PushBatchEntity progressBatch = createNewBatch(taskId, breakpoint);
            result.setBatchNo(progressBatch.getBatchNo());

            // 使用时间窗口限制的分页SQL
            String pagedSql = buildTimeBasedPagedSqlWithWindow(processedSql, lastProcessedTime, lastProcessedId,
                    breakpoint.getCurrentWindowEnd(), pushConfig);

            log.info("任务[{}]执行窗口内SQL查询，时间范围: {} ~ {}, 上次处理: 时间={}, ID={}, 批次大小: {}",
                    taskId, breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd(),
                    lastProcessedTime, lastProcessedId, pushConfig.getBatchSize());

            // 执行查询
            long queryStartTime = System.currentTimeMillis();
            SqlQueryResultVO queryResult = null;
            try {
                queryResult = SqlExecutorUtil.executeSql(dataSource, dbDriver, pagedSql, 0, null);
                long queryTime = System.currentTimeMillis() - queryStartTime;
                log.info("任务[{}]SQL查询完成，耗时: {}ms", taskId, queryTime);
            } catch (Exception e) {
                long queryTime = System.currentTimeMillis() - queryStartTime;
                log.error("任务[{}]SQL查询失败，耗时: {}ms, SQL: {}", taskId, queryTime, pagedSql, e);
                throw new RuntimeException("SQL查询执行失败: " + e.getMessage(), e);
            }

            // 检查查询结果
            if (queryResult == null || queryResult.getRows() == null || queryResult.getRows().isEmpty()) {
                log.info("任务[{}]当前时间窗口内无更多数据，窗口完成: {} ~ {}",
                        taskId, breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd());

                // 情况1：查询无数据，标记当前窗口完成
                breakpoint.setWindowCompleted(true);
                markBatchCompleted(progressBatch);

                // 检查是否需要切换到下一个窗口
                checkAndPrepareNextWindow(taskId, breakpoint, pushConfig);

            } else {
                List<Map<String, Object>> batchData = queryResult.getRows();
                log.info("任务[{}]查询到{}条数据", taskId, batchData.size());

                // 使用推送记录服务处理去重
                List<Map<String, Object>> filteredData = pushRecordService.processPushData(taskId, batchData, progressBatch.getBatchNo());

                int batchProcessed = batchData.size();
                int batchSuccess = filteredData.size();
                totalProcessedInWindow += batchProcessed;
                totalSuccessInWindow += batchSuccess;

                // 更新断点信息（时间+ID）- 但不立即保存到Redis
                BreakpointInfo newBreakpoint = getCurrentBatchBreakpoint(batchData, pushConfig);
                if (newBreakpoint.getLastId() != null) {
                    lastProcessedTime = newBreakpoint.getLastTime();
                    lastProcessedId = newBreakpoint.getLastId();

                    // 更新断点信息但保持窗口信息
                    breakpoint.setLastTime(lastProcessedTime);
                    breakpoint.setLastId(lastProcessedId);

                    // 🔥 关键修复：不在这里立即更新Redis，等接口调用成功后再更新
                    // 这样可以避免接口调用失败时Redis中记录错误的断点信息
                    log.info("任务[{}]批次处理完成，准备推送数据，断点: 时间={}, ID={}（待接口成功后更新到Redis）",
                            taskId, lastProcessedTime, lastProcessedId);
                }

                // 检查窗口完成条件 - 关键修复：满批次说明窗口数据未完成
                if (batchData.size() < pushConfig.getBatchSize()) {
                    // 情况2：数据量小于批次大小，当前窗口处理完成
                    log.info("任务[{}]数据量{}小于批次大小{}，当前窗口处理完成",
                            taskId, batchData.size(), pushConfig.getBatchSize());
                    breakpoint.setWindowCompleted(true);

                    // 检查是否需要切换到下一个窗口
                    checkAndPrepareNextWindow(taskId, breakpoint, pushConfig);
                } else {
                    // 情况3：满批次，窗口数据未完成，保持当前窗口状态
                    log.info("任务[{}]数据量{}等于批次大小{}，窗口数据未完成，等待下次执行",
                            taskId, batchData.size(), pushConfig.getBatchSize());
                    breakpoint.setWindowCompleted(false);
                }

                // 处理推送数据
                if (!filteredData.isEmpty()) {
                    log.info("任务[{}]需要推送{}条数据", taskId, filteredData.size());
                    // 设置返回数据（只返回最后一批的数据用于推送）
                    result.setFilteredData(filteredData);
                    result.setBatchEntity(progressBatch);
                } else {
                    log.info("任务[{}]无需推送数据（全部已处理），更新断点信息并完成批次", taskId);

                    // 🔥 关键修复：即使没有数据推送，也要更新批次中的断点信息
                    if (newBreakpoint.getLastId() != null) {
                        updateBatchEntityWithBreakpoint(progressBatch, newBreakpoint);
                    }

                    markBatchCompleted(progressBatch);
                }
            }

            result.setTotalProcessed(totalProcessedInWindow);
            result.setTotalSuccess(totalSuccessInWindow);
            result.setTotalFailed(0);
            result.setSuccess(true);

            log.info("任务[{}]时间窗口处理完成，总处理{}条，总推送{}条",
                    taskId, totalProcessedInWindow, totalSuccessInWindow);

        } catch (Exception e) {
            log.error("任务[{}]时间窗口处理失败", taskId, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 构建基于时间+ID的分页SQL（新的统一推送逻辑）
     *
     * 新的分页逻辑基于时间+ID进行排序和分页，确保：
     * 1. 数据按时间和ID有序处理
     * 2. 支持断点续传
     * 3. 不遗漏、不重复
     *
     * @param originalSql 原始的SQL查询语句
     * @param lastProcessedTime 上一批次处理的最后时间
     * @param lastProcessedId 上一批次处理的最后一个ID
     * @param pushConfig 推送配置
     * @return 构建好的分页SQL
     */
    private String buildTimeBasedPagedSql(String originalSql, String lastProcessedTime, String lastProcessedId, PushConfigEntity pushConfig) {
        // 移除原SQL中可能存在的ORDER BY和LIMIT语句
        String cleanSql = originalSql.replaceAll("(?i)\\s+order\\s+by\\s+[^;]*", "")
                .replaceAll("(?i)\\s+limit\\s+[^;]*", "");

        // 设置默认批次大小
        int pageSize = pushConfig.getBatchSize() != null ? pushConfig.getBatchSize() : 1000;
        String updateTimeField = pushConfig.getUpdateTimeField() != null ? pushConfig.getUpdateTimeField() : "update_time";
        String dataIdField = pushConfig.getTargetDataIdField() != null ? pushConfig.getTargetDataIdField() : "id";

        // 构建分页SQL
        StringBuilder pagedSql = new StringBuilder("SELECT * FROM (");
        pagedSql.append("SELECT inner_t.*,ROWNUM AS rn FROM (");
        pagedSql.append(cleanSql);

        // 添加分页条件
        boolean hasWhere = cleanSql.toLowerCase().contains("where");

        // 确定有效的起始时间（优先使用断点续传时间，其次使用配置的起始时间）
        String effectiveStartTime = null;
        if (lastProcessedTime != null && !lastProcessedTime.isEmpty()) {
            effectiveStartTime = lastProcessedTime;
        } else if (pushConfig.getStartTime() != null) {
            effectiveStartTime = pushConfig.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        // 添加时间条件（避免重复）
        if (effectiveStartTime != null) {
            if (hasWhere) {
                pagedSql.append(" AND ");
            } else {
                pagedSql.append(" WHERE ");
                hasWhere = true;
            }

            if (lastProcessedTime != null && !lastProcessedTime.isEmpty()) {
                // 断点续传：时间+ID的组合条件，确保正确的分页
                pagedSql.append("(").append(updateTimeField).append(" > TO_DATE('").append(effectiveStartTime).append("', 'YYYY-MM-DD HH24:MI:SS')");
                if (lastProcessedId != null && !lastProcessedId.isEmpty() && !"0".equals(lastProcessedId)) {
                    pagedSql.append(" OR (").append(updateTimeField).append(" = TO_DATE('").append(effectiveStartTime).append("', 'YYYY-MM-DD HH24:MI:SS')")
                            .append(" AND ").append(dataIdField).append(" > '").append(lastProcessedId).append("')");
                }
                pagedSql.append(")");
            } else {
                // 配置的起始时间：简单的大于等于条件
                pagedSql.append(updateTimeField).append(" >= TO_DATE('").append(effectiveStartTime).append("', 'YYYY-MM-DD HH24:MI:SS')");
            }
        }

        // 确保排序：先按时间，再按ID
        pagedSql.append(" ORDER BY ").append(updateTimeField).append(" ASC, ").append(dataIdField).append(" ASC");

        pagedSql.append(") inner_t WHERE ROWNUM <= ").append(pageSize);
        pagedSql.append(") WHERE rn >= 1");

        return pagedSql.toString();
    }

    /**
     * 构建基于时间窗口的分页SQL
     */
    private String buildTimeBasedPagedSqlWithWindow(String originalSql, String lastProcessedTime, String lastProcessedId, 
                                                   LocalDateTime windowEnd, PushConfigEntity pushConfig) {
        // 移除原SQL中可能存在的ORDER BY和LIMIT语句
        String cleanSql = originalSql.replaceAll("(?i)\\s+order\\s+by\\s+[^;]*", "")
                .replaceAll("(?i)\\s+limit\\s+[^;]*", "");

        // 设置默认批次大小
        int pageSize = pushConfig.getBatchSize() != null ? pushConfig.getBatchSize() : 1000;
        String updateTimeField = pushConfig.getUpdateTimeField() != null ? pushConfig.getUpdateTimeField() : "update_time";
        String dataIdField = pushConfig.getTargetDataIdField() != null ? pushConfig.getTargetDataIdField() : "id";

        // 构建分页SQL
        StringBuilder pagedSql = new StringBuilder("SELECT * FROM (");
        pagedSql.append("SELECT inner_t.*,ROWNUM AS rn FROM (");
        pagedSql.append(cleanSql);

        // 添加分页条件
        boolean hasWhere = cleanSql.toLowerCase().contains("where");

        // 确定有效的起始时间（优先使用断点续传时间，其次使用配置的起始时间）
        String effectiveStartTime = null;
        if (lastProcessedTime != null && !lastProcessedTime.isEmpty()) {
            effectiveStartTime = lastProcessedTime;
        } else if (pushConfig.getStartTime() != null) {
            effectiveStartTime = pushConfig.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        // 添加时间条件（避免重复）
        if (effectiveStartTime != null) {
            if (hasWhere) {
                pagedSql.append(" AND ");
            } else {
                pagedSql.append(" WHERE ");
                hasWhere = true;
            }

            if (lastProcessedTime != null && !lastProcessedTime.isEmpty()) {
                // 断点续传：时间+ID的组合条件，确保正确的分页
                pagedSql.append("(").append(updateTimeField).append(" > TO_DATE('").append(effectiveStartTime).append("', 'YYYY-MM-DD HH24:MI:SS')");
                if (lastProcessedId != null && !lastProcessedId.isEmpty() && !"0".equals(lastProcessedId)) {
                    pagedSql.append(" OR (").append(updateTimeField).append(" = TO_DATE('").append(effectiveStartTime).append("', 'YYYY-MM-DD HH24:MI:SS')")
                            .append(" AND ").append(dataIdField).append(" > '").append(lastProcessedId).append("')");
                }
                pagedSql.append(")");
            } else {
                // 配置的起始时间：简单的大于等于条件
                pagedSql.append(updateTimeField).append(" >= TO_DATE('").append(effectiveStartTime).append("', 'YYYY-MM-DD HH24:MI:SS')");
            }
        }

        // 添加时间窗口结束时间限制
        if (windowEnd != null) {
            String windowEndStr = windowEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (hasWhere) {
                pagedSql.append(" AND ");
            } else {
                pagedSql.append(" WHERE ");
            }
            pagedSql.append(updateTimeField).append(" <= TO_DATE('").append(windowEndStr).append("', 'YYYY-MM-DD HH24:MI:SS')");
        }

        // 确保排序：先按时间，再按ID
        pagedSql.append(" ORDER BY ").append(updateTimeField).append(" ASC, ").append(dataIdField).append(" ASC");

        pagedSql.append(") inner_t WHERE ROWNUM <= ").append(pageSize);
        pagedSql.append(") WHERE rn >= 1");

        return pagedSql.toString();
    }

    /**
     * 计算当前时间窗口 - 修复版本
     *
     * 关键修复：
     * 1. 如果有断点时间，下一窗口从断点时间开始，而不是配置的起始时间
     * 2. 确保窗口计算的连续性和正确性
     */
    private void calculateCurrentTimeWindow(BreakpointInfo breakpoint, PushConfigEntity pushConfig) {
        // 获取时间窗口大小（小时），默认1小时
        int timeWindowHours = pushConfig.getTimeWindowHours() != null ? pushConfig.getTimeWindowHours() : 1;
        int timeWindowMinutes = timeWindowHours * 60; // 转换为分钟用于计算

        LocalDateTime windowStart;

        if (breakpoint.getCurrentWindowStart() != null && breakpoint.getCurrentWindowEnd() != null && !breakpoint.isWindowCompleted()) {
            // 如果当前窗口未完成，继续使用当前窗口
            log.info("继续使用当前未完成的时间窗口: {} ~ {}",
                    breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd());
            return;
        }

        // 关键修复：优先使用断点时间作为窗口起始时间
        if (breakpoint.getLastTime() != null && !breakpoint.getLastTime().isEmpty()) {
            // 基于上次处理时间计算窗口 - 这是断点续传的关键
            try {
                LocalDateTime lastTime = LocalDateTime.parse(breakpoint.getLastTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                windowStart = lastTime; // 从断点时间开始，而不是配置的起始时间
                log.info("基于断点时间计算窗口起始时间: {}", windowStart);
            } catch (Exception e) {
                log.warn("解析断点时间失败: {}, 使用配置起始时间", breakpoint.getLastTime());
                windowStart = pushConfig.getStartTime() != null ? pushConfig.getStartTime() : LocalDateTime.now().minusHours(1);
            }
        } else {
            // 首次执行，使用配置的起始时间
            windowStart = pushConfig.getStartTime() != null ? pushConfig.getStartTime() : LocalDateTime.now().minusHours(1);
            log.info("首次执行，使用配置起始时间: {}", windowStart);
        }

        LocalDateTime windowEnd = windowStart.plusMinutes(timeWindowMinutes);

        breakpoint.setCurrentWindowStart(windowStart);
        breakpoint.setCurrentWindowEnd(windowEnd);
        breakpoint.setWindowCompleted(false);

        log.info("计算时间窗口: {} ~ {}, 窗口大小: {}小时", windowStart, windowEnd, timeWindowHours);
    }

    /**
     * 检查并准备下一个时间窗口
     *
     * 关键逻辑：
     * 1. 如果下一窗口也无数据 → 说明已处理到最新数据
     * 2. 保持最大时间和ID不变，等待新数据
     */
    private void checkAndPrepareNextWindow(Long taskId, BreakpointInfo breakpoint, PushConfigEntity pushConfig) {
        if (!breakpoint.isWindowCompleted()) {
            return; // 当前窗口未完成，无需准备下一窗口
        }

        // 准备下一个时间窗口
        prepareNextTimeWindow(breakpoint, pushConfig);

        // 保存下一窗口的断点信息到Redis
        saveBreakpointToRedis(taskId, breakpoint);

        log.info("任务[{}]已准备下一时间窗口: {} ~ {}, 起始断点: 时间={}, ID={}",
                taskId, breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd(),
                breakpoint.getLastTime(), breakpoint.getLastId());
    }
    
    /**
     * 准备下一个时间窗口 - 修复版本
     *
     * 关键修复：
     * 1. 下一窗口的开始时间 = 上次记录的最大时间和ID（不是配置的推送起始时间）
     * 2. 如果下一窗口也无数据 → 说明已处理到最新数据，保持最大时间和ID不变
     */
    private void prepareNextTimeWindow(BreakpointInfo breakpoint, PushConfigEntity pushConfig) {
        // 获取时间窗口大小（小时），默认1小时
        int timeWindowHours = pushConfig.getTimeWindowHours() != null ? pushConfig.getTimeWindowHours() : 1;
        int timeWindowMinutes = timeWindowHours * 60; // 转换为分钟用于计算

        // 关键修复：下一个窗口从上次记录的最大时间和ID开始
        LocalDateTime nextWindowStart;
        if (breakpoint.getLastTime() != null && !breakpoint.getLastTime().isEmpty()) {
            try {
                // 使用断点时间作为下一窗口的起始时间
                nextWindowStart = LocalDateTime.parse(breakpoint.getLastTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                log.info("下一窗口基于断点时间开始: {}", nextWindowStart);
            } catch (Exception e) {
                log.warn("解析断点时间失败: {}, 使用当前窗口结束时间", breakpoint.getLastTime());
                nextWindowStart = breakpoint.getCurrentWindowEnd();
            }
        } else {
            // 如果没有断点时间，使用当前窗口结束时间
            nextWindowStart = breakpoint.getCurrentWindowEnd();
            log.info("下一窗口基于当前窗口结束时间开始: {}", nextWindowStart);
        }

        LocalDateTime nextWindowEnd = nextWindowStart.plusMinutes(timeWindowMinutes);

        // 检查下一窗口是否超过当前时间
        LocalDateTime now = LocalDateTime.now();
        if (nextWindowEnd.isAfter(now)) {
            // 如果下一窗口结束时间超过当前时间，调整为当前时间
            nextWindowEnd = now;
            log.info("下一窗口结束时间调整为当前时间: {}", nextWindowEnd);
        }

        breakpoint.setCurrentWindowStart(nextWindowStart);
        breakpoint.setCurrentWindowEnd(nextWindowEnd);
        breakpoint.setWindowCompleted(false);

        log.info("准备下一时间窗口: {} ~ {}, 窗口大小: {}小时", nextWindowStart, nextWindowEnd, timeWindowHours);
    }

    @Resource
    private RedisDeduplicationService redisDeduplicationService;

    /**
     * 获取上次处理的断点信息（时间+ID）- 修复版本
     *
     * 关键修复：
     * 1. 优先检查是否有进行中的批次（断点续传）
     * 2. 正确处理首次执行的情况
     * 3. 确保断点信息的完整性和准确性
     */
    private BreakpointInfo getLastBreakpointFromHistory(Long taskId) {
        // 获取推送配置
        PushConfigEntity pushConfig = pushConfigDao.selectByTaskId(taskId);
        if (pushConfig == null) {
            pushConfig = pushRecordService.createDefaultPushConfig(taskId);
        }

        // 优先从Redis获取断点信息（最新的断点状态）
        BreakpointInfo breakpoint = getBreakpointFromRedis(taskId);
        if (breakpoint != null && (breakpoint.getLastTime() != null || (breakpoint.getLastId() != null && !"0".equals(breakpoint.getLastId())))) {
            log.debug("任务[{}]从Redis获取断点信息: 时间={}, ID={}", taskId, breakpoint.getLastTime(), breakpoint.getLastId());
            return breakpoint;
        }

        // 检查是否有进行中的批次（断点续传场景）
        PushBatchEntity inProgressBatch = pushBatchDao.selectInProgressBatchByTaskId(taskId);
        if (inProgressBatch != null) {
            log.info("任务[{}]发现进行中的批次[{}]，从断点继续", taskId, inProgressBatch.getBatchNo());
            try {
                String progressInfoJson = inProgressBatch.getProgressInfo();
                if (progressInfoJson != null && !progressInfoJson.isEmpty()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> progressInfo = objectMapper.readValue(progressInfoJson, Map.class);
                    String lastTime = (String) progressInfo.get("lastProcessedTime");
                    String lastId = progressInfo.get("lastProcessedId") != null ? progressInfo.get("lastProcessedId").toString() : "0";

                    BreakpointInfo inProgressBreakpoint = new BreakpointInfo(lastTime, lastId);
                    // 同步到Redis中
                    saveBreakpointToRedis(taskId, inProgressBreakpoint);
                    log.info("任务[{}]从进行中批次获取断点信息: 时间={}, ID={}，已同步到Redis", taskId, lastTime, lastId);
                    return inProgressBreakpoint;
                }
            } catch (Exception e) {
                log.warn("任务[{}]解析进行中批次进度信息失败", taskId, e);
            }
        }

        // 从最新完成的批次获取断点信息
        PushBatchEntity lastCompletedBatch = pushBatchDao.selectLatestCompletedByTaskId(taskId);
        if (lastCompletedBatch != null) {
            try {
                String progressInfoJson = lastCompletedBatch.getProgressInfo();
                if (progressInfoJson != null && !progressInfoJson.isEmpty()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> progressInfo = objectMapper.readValue(progressInfoJson, Map.class);
                    String lastTime = (String) progressInfo.get("lastProcessedTime");
                    String lastId = progressInfo.get("lastProcessedId") != null ? progressInfo.get("lastProcessedId").toString() : "0";

                    BreakpointInfo dbBreakpoint = new BreakpointInfo(lastTime, lastId);
                    // 同步到Redis中，下次直接从Redis获取
                    saveBreakpointToRedis(taskId, dbBreakpoint);
                    log.debug("任务[{}]从数据库获取断点信息: 时间={}, ID={}，已同步到Redis", taskId, lastTime, lastId);
                    return dbBreakpoint;
                }
            } catch (Exception e) {
                log.warn("任务[{}]解析上次批次进度信息失败", taskId, e);
            }
        }

        // 首次执行的情况
        if (pushConfig.getStartTime() != null) {
            // 如果设置了起始时间，从起始时间开始
            String startTimeStr = pushConfig.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            BreakpointInfo firstTimeBreakpoint = new BreakpointInfo(startTimeStr, "0");
            log.info("任务[{}]首次执行，从配置起始时间开始: {}", taskId, startTimeStr);
            return firstTimeBreakpoint;
        } else {
            // 没有配置起始时间，从头开始
            log.debug("任务[{}]首次执行，从头开始", taskId);
            return new BreakpointInfo(); // 返回空断点，表示从头开始
        }
    }

    /**
     * 从Redis获取断点信息 - 完整版本
     *
     * 🔥 关键修复：获取完整的断点信息，包括时间窗口状态
     */
    private BreakpointInfo getBreakpointFromRedis(Long taskId) {
        try {
            // 获取基础断点信息
            String lastTime = redisDeduplicationService.getLastProcessedTime(taskId);
            String lastId = redisDeduplicationService.getMaxProcessedId(taskId);

            // 获取时间窗口信息
            String windowInfoJson = redisDeduplicationService.getTimeWindowInfo(taskId);

            if (lastTime != null || (lastId != null && !"0".equals(lastId)) || windowInfoJson != null) {
                BreakpointInfo breakpoint = new BreakpointInfo(lastTime, lastId);

                // 🔥 关键修复：恢复时间窗口状态
                if (windowInfoJson != null && !windowInfoJson.isEmpty()) {
                    restoreTimeWindowInfo(breakpoint, windowInfoJson);
                }

                log.debug("任务[{}]从Redis获取完整断点信息: 时间={}, ID={}, 窗口={} ~ {}, 完成={}",
                        taskId, breakpoint.getLastTime(), breakpoint.getLastId(),
                        breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd(),
                        breakpoint.isWindowCompleted());

                return breakpoint;
            }
        } catch (Exception e) {
            log.warn("任务[{}]从Redis获取断点信息失败", taskId, e);
        }
        return null;
    }

    /**
     * 从JSON恢复时间窗口信息
     *
     * @param breakpoint 断点信息对象
     * @param windowInfoJson 时间窗口信息JSON
     */
    private void restoreTimeWindowInfo(BreakpointInfo breakpoint, String windowInfoJson) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> windowInfo = objectMapper.readValue(windowInfoJson, Map.class);

            // 恢复窗口开始时间
            String windowStartStr = (String) windowInfo.get("windowStart");
            if (windowStartStr != null) {
                LocalDateTime windowStart = LocalDateTime.parse(windowStartStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                breakpoint.setCurrentWindowStart(windowStart);
            }

            // 恢复窗口结束时间
            String windowEndStr = (String) windowInfo.get("windowEnd");
            if (windowEndStr != null) {
                LocalDateTime windowEnd = LocalDateTime.parse(windowEndStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                breakpoint.setCurrentWindowEnd(windowEnd);
            }

            // 恢复窗口完成状态
            Boolean windowCompleted = (Boolean) windowInfo.get("windowCompleted");
            if (windowCompleted != null) {
                breakpoint.setWindowCompleted(windowCompleted);
            }

            log.debug("成功恢复时间窗口信息: {} ~ {}, 完成={}",
                    breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd(),
                    breakpoint.isWindowCompleted());

        } catch (Exception e) {
            log.warn("恢复时间窗口信息失败: {}", windowInfoJson, e);
        }
    }

    /**
     * 保存断点信息到Redis - 完整版本
     *
     * 🔥 关键修复：保存完整的断点信息，包括时间窗口状态
     * 这样重启后可以准确恢复推送周期的状态
     */
    private void saveBreakpointToRedis(Long taskId, BreakpointInfo breakpoint) {
        try {
            // 保存基础断点信息（时间+ID）
            if (breakpoint.getLastTime() != null) {
                redisDeduplicationService.saveLastProcessedTime(taskId, breakpoint.getLastTime());
            }
            if (breakpoint.getLastId() != null) {
                redisDeduplicationService.saveMaxProcessedId(taskId, breakpoint.getLastId());
            }

            // 🔥 关键修复：保存完整的时间窗口信息
            saveTimeWindowInfoToRedis(taskId, breakpoint);

            log.debug("任务[{}]完整断点信息已保存到Redis: 时间={}, ID={}, 窗口={} ~ {}, 完成={}",
                    taskId, breakpoint.getLastTime(), breakpoint.getLastId(),
                    breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd(),
                    breakpoint.isWindowCompleted());

        } catch (Exception e) {
            log.warn("任务[{}]保存断点信息到Redis失败", taskId, e);
        }
    }

    /**
     * 保存时间窗口信息到Redis
     *
     * 保存推送周期的关键信息：
     * - 当前窗口开始时间
     * - 当前窗口结束时间
     * - 窗口是否完成
     */
    private void saveTimeWindowInfoToRedis(Long taskId, BreakpointInfo breakpoint) {
        try {
            // 构建时间窗口信息的JSON
            Map<String, Object> windowInfo = new HashMap<>();

            if (breakpoint.getCurrentWindowStart() != null) {
                windowInfo.put("windowStart", breakpoint.getCurrentWindowStart().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (breakpoint.getCurrentWindowEnd() != null) {
                windowInfo.put("windowEnd", breakpoint.getCurrentWindowEnd().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            windowInfo.put("windowCompleted", breakpoint.isWindowCompleted());
            windowInfo.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 保存到Redis（使用JSON格式）
            String windowInfoJson = objectMapper.writeValueAsString(windowInfo);
            redisDeduplicationService.saveTimeWindowInfo(taskId, windowInfoJson);

            log.debug("任务[{}]时间窗口信息已保存到Redis: {}", taskId, windowInfoJson);

        } catch (Exception e) {
            log.warn("任务[{}]保存时间窗口信息到Redis失败", taskId, e);
        }
    }

    /**
     * 更新批次实体中的断点信息
     *
     * 用于在没有数据推送时，也能正确更新批次中的断点信息
     */
    private void updateBatchEntityWithBreakpoint(PushBatchEntity batchEntity, BreakpointInfo breakpoint) {
        try {
            // 获取当前进度信息
            Map<String, Object> progressInfo = new HashMap<>();
            String currentProgressJson = batchEntity.getProgressInfo();
            if (currentProgressJson != null && !currentProgressJson.isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> currentProgress = objectMapper.readValue(currentProgressJson, Map.class);
                progressInfo.putAll(currentProgress);
            }

            // 更新断点信息
            if (breakpoint.getLastId() != null) {
                progressInfo.put("lastProcessedId", breakpoint.getLastId());
            }
            if (breakpoint.getLastTime() != null) {
                progressInfo.put("lastProcessedTime", breakpoint.getLastTime());
            }
            progressInfo.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 更新批次实体
            batchEntity.setProgressInfo(convertToJson(progressInfo));
            batchEntity.setUpdateTime(LocalDateTime.now());

            log.debug("已更新批次[{}]的断点信息: 时间={}, ID={}",
                    batchEntity.getBatchNo(), breakpoint.getLastTime(), breakpoint.getLastId());

        } catch (Exception e) {
            log.error("更新批次[{}]断点信息失败", batchEntity.getBatchNo(), e);
        }
    }

    /**
     * 创建新的批次记录（支持断点信息）
     */
    private PushBatchEntity createNewBatch(Long taskId, BreakpointInfo breakpoint) {
        // 创建新的批次记录
        PushBatchEntity newBatch = new PushBatchEntity();
        newBatch.setTaskId(taskId);
        newBatch.setBatchNo(generateBatchNo(taskId));
        newBatch.setTotalCount(0);
        newBatch.setSuccessCount(0);
        newBatch.setFailedCount(0);
        newBatch.setBatchStatus(PushBatchEntity.BatchStatus.IN_PROGRESS);
        newBatch.setCurrentPage(1);
        newBatch.setTotalPages(0);
        newBatch.setStartTime(LocalDateTime.now());
        newBatch.setCreateTime(LocalDateTime.now());
        newBatch.setUpdateTime(LocalDateTime.now());

        // 初始化进度信息（使用断点信息）
        Map<String, Object> progressInfo = new HashMap<>();
        progressInfo.put("lastProcessedId", breakpoint.getLastId());
        progressInfo.put("lastProcessedTime", breakpoint.getLastTime());
        progressInfo.put("startTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        newBatch.setProgressInfo(convertToJson(progressInfo));

        pushBatchDao.insert(newBatch);
        log.info("为任务[{}]创建新批次[{}]，断点时间: {}, 断点ID: {}",
                taskId, newBatch.getBatchNo(), breakpoint.getLastTime(), breakpoint.getLastId());

        return newBatch;
    }

    /**
     * 获取当前批次的断点信息（时间+ID）
     */
    private BreakpointInfo getCurrentBatchBreakpoint(List<Map<String, Object>> batchData, PushConfigEntity pushConfig) {
        if (batchData == null || batchData.isEmpty()) {
            return new BreakpointInfo();
        }

        // 获取最后一条数据作为新的断点
        Map<String, Object> lastRecord = batchData.get(batchData.size() - 1);

        String targetDataIdField = pushConfig.getTargetDataIdField() != null ? pushConfig.getTargetDataIdField() : "id";
        String updateTimeField = pushConfig.getUpdateTimeField() != null ? pushConfig.getUpdateTimeField() : "update_time";

        Object maxId = lastRecord.get(targetDataIdField);
        Object maxTime = lastRecord.get(updateTimeField);

        String lastId = maxId != null ? maxId.toString() : null;
        String lastTime = maxTime != null ? maxTime.toString() : null;

        return new BreakpointInfo(lastTime, lastId);
    }

    /**
     * 获取当前批次的最大ID（兼容旧方法）
     */
    private String getCurrentBatchMaxId(List<Map<String, Object>> batchData, String targetDataIdField) {
        if (batchData == null || batchData.isEmpty()) {
            return null;
        }

        // 获取最后一条数据的主键作为最大ID
        Map<String, Object> lastRecord = batchData.get(batchData.size() - 1);

        // 优先使用主键字段ID，如果没有则使用指定的dataIdField
        Object maxId = lastRecord.get(targetDataIdField); // 查询中的别名
        return maxId != null ? maxId.toString() : null;
    }

    /**
     * 更新批次处理进度（基于主键分页）
     */
    private void updateBatchProgress(PushBatchEntity batch, List<Map<String, Object>> batchData,
                                   String dataIdField, int totalProcessed, int totalSuccess) {
        if (batchData.isEmpty()) {
            return;
        }

        // 获取最后一条数据的主键作为新的处理位置
        Map<String, Object> lastRecord = batchData.get(batchData.size() - 1);

        // 优先使用主键字段PFPTABLE002CA00（对应查询中的ID字段），如果没有则使用指定的dataIdField
        Object lastId = lastRecord.get("ID"); // 查询中的别名
        if (lastId == null) {
            lastId = lastRecord.get("PFPTABLE002CA00"); // 原始字段名
        }
        if (lastId == null) {
            lastId = lastRecord.get(dataIdField); // 兜底使用指定字段
        }

        if (lastId != null) {
            try {
                // 更新进度信息
                Map<String, Object> progressInfo = new HashMap<>();
                progressInfo.put("lastProcessedId", lastId.toString());
                progressInfo.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                progressInfo.put("totalProcessedSoFar", batch.getTotalCount() + totalProcessed);
                progressInfo.put("totalSuccessSoFar", batch.getSuccessCount() + totalSuccess);

                batch.setProgressInfo(convertToJson(progressInfo));
                batch.setTotalCount(batch.getTotalCount() + totalProcessed);
                batch.setSuccessCount(batch.getSuccessCount() + totalSuccess);
                batch.setUpdateTime(LocalDateTime.now());

                pushBatchDao.updateById(batch);
                log.debug("更新任务[{}]处理进度，最新ID: {}", batch.getTaskId(), lastId);
            } catch (Exception e) {
                log.error("更新进度信息失败", e);
            }
        }
    }

    /**
     * 标记批次为已完成 - 修复版本
     *
     * 关键修复：确保断点信息（时间+ID）都能正确保存到Redis
     */
    private void markBatchCompleted(PushBatchEntity batch) {
        try {
            // 更新进度信息
            Map<String, Object> progressInfo = new HashMap<>();
            String currentProgressJson = batch.getProgressInfo();
            if (currentProgressJson != null && !currentProgressJson.isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> currentProgress = objectMapper.readValue(currentProgressJson, Map.class);
                progressInfo.putAll(currentProgress);
            }
            progressInfo.put("completedTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progressInfo.put("status", "completed");

            batch.setProgressInfo(convertToJson(progressInfo));
            batch.setBatchStatus(PushBatchEntity.BatchStatus.COMPLETED);
            batch.setEndTime(LocalDateTime.now());
            batch.setUpdateTime(LocalDateTime.now());

            pushBatchDao.updateById(batch);

            // 🔥 关键修复：同时更新Redis中的断点信息（时间+ID）
            Object lastProcessedId = progressInfo.get("lastProcessedId");
            Object lastProcessedTime = progressInfo.get("lastProcessedTime");

            if (lastProcessedId != null || lastProcessedTime != null) {
                // 保存完整的断点信息到Redis
                if (lastProcessedId != null) {
                    redisDeduplicationService.saveMaxProcessedId(batch.getTaskId(), lastProcessedId.toString());
                }
                if (lastProcessedTime != null) {
                    redisDeduplicationService.saveLastProcessedTime(batch.getTaskId(), lastProcessedTime.toString());
                }
                log.info("任务[{}]批次[{}]处理完成，断点信息已同步到Redis: 时间={}, ID={}",
                        batch.getTaskId(), batch.getBatchNo(), lastProcessedTime, lastProcessedId);
            } else {
                log.info("任务[{}]批次[{}]处理完成（无断点信息）", batch.getTaskId(), batch.getBatchNo());
            }
        } catch (Exception e) {
            log.error("标记批次完成失败", e);
        }
    }

    /**
     * 转换为JSON字符串
     */
    private String convertToJson(Map<String, Object> data) {
        try {
            return objectMapper.writeValueAsString(data);
        } catch (Exception e) {
            log.warn("转换JSON失败，使用toString", e);
            return data.toString();
        }
    }

    /**
     * 生成批次号
     */
    private String generateBatchNo(Long taskId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format("BATCH_%d_%s_%d", taskId, timestamp, System.currentTimeMillis() % 1000);
    }

    /**
     * 接口调用成功后更新批次进度（支持新的断点逻辑）
     *
     * @param batchEntity 批次实体
     * @param batchData 本次处理的数据
     * @param pushConfig 推送配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchProgressAfterSuccess(PushBatchEntity batchEntity, List<Map<String, Object>> batchData, PushConfigEntity pushConfig) {
        if (batchEntity == null || batchData == null || batchData.isEmpty()) {
            return;
        }

        try {
            BreakpointInfo newBreakpoint = getCurrentBatchBreakpoint(batchData, pushConfig);
            if (newBreakpoint.getLastId() == null) {
                log.warn("无法获取批次[{}]的断点信息，跳过进度更新", batchEntity.getBatchNo());
                return;
            }

            // 更新进度信息
            Map<String, Object> progressInfo = new HashMap<>();
            String currentProgressJson = batchEntity.getProgressInfo();
            if (currentProgressJson != null && !currentProgressJson.isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> currentProgress = objectMapper.readValue(currentProgressJson, Map.class);
                progressInfo.putAll(currentProgress);
            }

            // 更新实际处理的断点信息（接口调用成功，可以安全更新）
            progressInfo.put("lastProcessedId", newBreakpoint.getLastId());
            progressInfo.put("lastProcessedTime", newBreakpoint.getLastTime());
            progressInfo.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progressInfo.put("interfaceCallSuccess", true);
            progressInfo.put("completedTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progressInfo.put("status", "completed");

            batchEntity.setProgressInfo(convertToJson(progressInfo));
            batchEntity.setBatchStatus(PushBatchEntity.BatchStatus.COMPLETED);
            batchEntity.setEndTime(LocalDateTime.now());
            batchEntity.setUpdateTime(LocalDateTime.now());

            pushBatchDao.updateById(batchEntity);

            // 🔥 关键修复：同时更新Redis中的断点信息
            saveBreakpointToRedis(batchEntity.getTaskId(), newBreakpoint);

            log.info("接口调用成功，批次[{}]进度已更新，新断点: 时间={}, ID={}，已同步到Redis",
                    batchEntity.getBatchNo(), newBreakpoint.getLastTime(), newBreakpoint.getLastId());

        } catch (Exception e) {
            log.error("更新批次[{}]进度失败", batchEntity.getBatchNo(), e);
        }
    }

    /**
     * 接口调用失败后标记批次状态
     *
     * @param batchEntity 批次实体
     * @param errorMessage 错误信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void markBatchFailedAfterInterfaceError(PushBatchEntity batchEntity, String errorMessage) {
        if (batchEntity == null) {
            return;
        }

        try {
            // 更新进度信息，但不更新lastProcessedId（保持上次成功的位置）
            Map<String, Object> progressInfo = new HashMap<>();
            String currentProgressJson = batchEntity.getProgressInfo();
            if (currentProgressJson != null && !currentProgressJson.isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> currentProgress = objectMapper.readValue(currentProgressJson, Map.class);
                progressInfo.putAll(currentProgress);
            }

            progressInfo.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progressInfo.put("interfaceCallSuccess", false);
            progressInfo.put("interfaceErrorMessage", errorMessage);

            batchEntity.setProgressInfo(convertToJson(progressInfo));
            batchEntity.setBatchStatus(PushBatchEntity.BatchStatus.FAILED);
            batchEntity.setUpdateTime(LocalDateTime.now());

            pushBatchDao.updateById(batchEntity);
            log.warn("接口调用失败，批次[{}]状态已标记为失败，保持上次成功的进度ID", batchEntity.getBatchNo());

        } catch (Exception e) {
            log.error("标记批次[{}]失败状态时出错", batchEntity.getBatchNo(), e);
        }
    }

    /**
     * 断点信息类
     */
    @Setter
    @Getter
    public static class BreakpointInfo {
        private String lastTime;  // 上次处理的时间
        private String lastId;    // 上次处理的ID
        private LocalDateTime currentWindowStart;  // 当前时间窗口开始时间
        private LocalDateTime currentWindowEnd;    // 当前时间窗口结束时间
        private boolean windowCompleted;           // 当前窗口是否已完成

        public BreakpointInfo() {
            this.lastTime = null;
            this.lastId = "0";
            this.windowCompleted = false;
        }

        public BreakpointInfo(String lastTime, String lastId) {
            this.lastTime = lastTime;
            this.lastId = lastId != null ? lastId : "0";
            this.windowCompleted = false;
        }

        public BreakpointInfo(String lastTime, String lastId, LocalDateTime windowStart, LocalDateTime windowEnd) {
            this.lastTime = lastTime;
            this.lastId = lastId != null ? lastId : "0";
            this.currentWindowStart = windowStart;
            this.currentWindowEnd = windowEnd;
            this.windowCompleted = false;
        }
    }

    /**
     * 批次处理结果
     */
    @Setter
    @Getter
    public static class BatchProcessingResult {
        // Getters and Setters
        private String batchNo;
        private boolean success;
        private String errorMessage;
        private int totalProcessed;
        private int totalSuccess;
        private int totalFailed;
        private List<Map<String, Object>> filteredData; // 返回过滤后的数据
        private PushBatchEntity batchEntity; // 批次实体，用于后续更新

    }
}
