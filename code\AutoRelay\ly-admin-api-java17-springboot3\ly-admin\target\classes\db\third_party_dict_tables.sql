-- 第三方字典相关表初始化脚本
-- 创建时间：2023-06-01

-- 创建第三方系统表
CREATE TABLE IF NOT EXISTS `t_third_party_system` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `system_name` varchar(100) NOT NULL COMMENT '系统名称',
  `system_desc` varchar(500) DEFAULT NULL COMMENT '系统描述',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0停用 1启用',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_code` (`system_code`,`deleted_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方系统表';


-- 创建第三方字典类型表
CREATE TABLE IF NOT EXISTS `t_third_party_dict_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `dict_code` varchar(50) NOT NULL COMMENT '字典编码',
  `dict_name` varchar(50) NOT NULL COMMENT '字典名称',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_dict` (`system_code`,`dict_code`,`deleted_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方字典类型表';

-- 创建第三方字典值表
CREATE TABLE IF NOT EXISTS `t_third_party_dict_value` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `dict_type_id` bigint(20) NOT NULL COMMENT '字典类型ID',
  `dict_value` varchar(50) NOT NULL COMMENT '字典值',
  `dict_name` varchar(50) NOT NULL COMMENT '字典名称',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_value` (`dict_type_id`,`dict_value`,`deleted_flag`) USING BTREE,
  KEY `idx_system_code` (`system_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方字典值表';