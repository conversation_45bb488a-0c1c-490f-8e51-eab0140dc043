<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.interfaceConfig.dao.InterfaceParamMappingDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.InterfaceParamMappingEntity">
        <id column="id" property="id" />
        <result column="interface_id" property="interfaceId" />
        <result column="param_name" property="paramName" />
        <result column="target_param_name" property="targetParamName" />
        <result column="dict_type" property="dictType" />
        <result column="required" property="required" />
        <result column="filter_null_value" property="filterNullValue" />
        <result column="data_type" property="dataType" />
        <result column="default_value" property="defaultValue" />
        <result column="deleted_flag" property="deletedFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, interface_id, param_name, target_param_name, dict_type, required, filter_null_value, data_type, default_value, deleted_flag, create_time, update_time
    </sql>

    <!-- 根据接口ID查询参数映射列表 -->
    <select id="selectByInterfaceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_interface_param_mapping
        WHERE interface_id = #{interfaceId}
        AND deleted_flag = 0
    </select>

    <!-- 根据接口ID删除参数映射 -->
    <update id="deleteByInterfaceId">
        DELETE FROM t_interface_param_mapping
        WHERE interface_id = #{interfaceId}
    </update>

    <!-- 批量插入参数映射 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_interface_param_mapping
        (interface_id, param_name, target_param_name, dict_type, required, filter_null_value, data_type, default_value, deleted_flag, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.interfaceId}, #{item.paramName}, #{item.targetParamName}, #{item.dictType},
            #{item.required}, #{item.filterNullValue}, #{item.dataType}, #{item.defaultValue}, 0, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 根据ID查询参数映射 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_interface_param_mapping
        WHERE id = #{id}
        AND deleted_flag = 0
    </select>

    <!-- 批量删除参数映射（逻辑删除） -->
    <update id="batchDelete">
        UPDATE t_interface_param_mapping
        SET deleted_flag = 1,
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_flag = 0
    </update>
</mapper>
