<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.interfaceTask.dao.InterfaceTaskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.interfaceTask.domain.entity.InterfaceTaskEntity">
        <id column="id" property="id"/>
        <result column="task_name" property="taskName"/>
        <result column="task_description" property="taskDescription"/>
        <result column="interface_id" property="interfaceId"/>
        <result column="interface_name" property="interfaceName"/>
        <result column="system_code" property="systemCode"/>
        <result column="data_sql" property="dataSql"/>
        <result column="trigger_type" property="triggerType"/>
        <result column="trigger_value" property="triggerValue"/>
        <result column="enabled_flag" property="enabledFlag"/>
        <result column="last_execute_time" property="lastExecuteTime"/>
        <result column="last_execute_status" property="lastExecuteStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="deleted_flag" property="deletedFlag"/>
    </resultMap>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lingyue.ly.admin.autoRealy.interfaceTask.domain.vo.InterfaceTaskVO">
        SELECT
            t.id,
            t.task_name,
            t.task_description,
            t.interface_id,
            t.interface_name,
            t.system_code,
            t.data_sql,
            t.trigger_type,
            t.trigger_value,
            t.enabled_flag,
            t.last_execute_time,
            t.last_execute_status,
            t.create_time,
            t.update_time
        FROM
            t_interface_task t
        WHERE
            t.deleted_flag = 0
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (task_name LIKE CONCAT('%', #{queryForm.searchWord}, '%')
                OR system_code LIKE CONCAT('%', #{queryForm.searchWord}, '%')
                OR interface_name LIKE CONCAT('%', #{queryForm.searchWord}, '%'))
            </if>
            <if test="query.enabledFlag != null">
                AND enabled_flag = #{queryForm.enabledFlag}
            </if>
            <if test="query.triggerType != null and query.triggerType != ''">
                AND trigger_type = #{queryForm.triggerType}
            </if>
        ORDER BY t.create_time DESC
    </select>

    <!-- 查询所有启用的任务 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            t_interface_task
        WHERE
            deleted_flag = 0
          AND enabled_flag = 1
    </select>

    <!-- 根据接口ID查询关联的任务 -->
    <select id="selectByInterfaceId" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            t_interface_task
        WHERE
            deleted_flag = 0
          AND interface_id = #{interfaceId}
    </select>

    <!-- 假删除 -->
    <update id="updateDeletedFlag">
        UPDATE
            t_interface_task
        SET
            deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE
            id = #{id}
    </update>
</mapper>
