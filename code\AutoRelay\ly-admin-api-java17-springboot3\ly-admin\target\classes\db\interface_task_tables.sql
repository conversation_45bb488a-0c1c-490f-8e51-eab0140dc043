-- 接口任务相关表初始化脚本
-- 创建时间：2024-07-07

-- 创建接口任务表
CREATE TABLE IF NOT EXISTS `t_interface_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_description` varchar(255) DEFAULT NULL COMMENT '任务描述',
  `interface_id` bigint(20) NOT NULL COMMENT '关联的接口配置ID',
  `interface_name` varchar(100) NOT NULL COMMENT '接口名称（冗余）',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码（冗余）',
  `data_source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `data_sql` text DEFAULT NULL COMMENT '数据获取SQL',
  `trigger_type` varchar(20) NOT NULL COMMENT '触发类型：CRON表达式 或 FIXED_DELAY',
  `trigger_value` varchar(100) NOT NULL COMMENT '触发值：CRON表达式 或 固定间隔（秒）',
  `enabled_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0禁用 1启用',
  `last_execute_time` datetime DEFAULT NULL COMMENT '最后一次执行时间',
  `last_execute_status` tinyint(1) DEFAULT NULL COMMENT '最后一次执行状态：0-失败，1-成功',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_user_id` bigint(20) NOT NULL COMMENT '更新人ID',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  PRIMARY KEY (`id`),
  KEY `idx_interface_id` (`interface_id`) USING BTREE,
  KEY `idx_data_source_id` (`data_source_id`) USING BTREE,
  KEY `idx_enabled_flag` (`enabled_flag`) USING BTREE,
  KEY `idx_deleted_flag` (`deleted_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口任务表';

-- 创建接口任务执行日志表
CREATE TABLE IF NOT EXISTS `t_interface_task_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `success` tinyint(1) NOT NULL COMMENT '是否成功 0失败 1成功',
  `execute_result` text DEFAULT NULL COMMENT '上报数据详情',
  `request_body` text DEFAULT NULL COMMENT '请求体',
  `response_body` text DEFAULT NULL COMMENT '响应体',
  `status_code` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
  `execute_start_time` datetime NOT NULL COMMENT '执行开始时间',
  `execute_end_time` datetime NOT NULL COMMENT '执行结束时间',
  `execute_time_millis` bigint(20) NOT NULL COMMENT '执行时长（毫秒）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`) USING BTREE,
  KEY `idx_success` (`success`) USING BTREE,
  KEY `idx_execute_start_time` (`execute_start_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口任务执行日志表';