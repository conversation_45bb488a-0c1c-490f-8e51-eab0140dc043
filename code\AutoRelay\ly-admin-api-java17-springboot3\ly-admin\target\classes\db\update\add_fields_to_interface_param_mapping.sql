-- 向t_interface_param_mapping表添加新字段
ALTER TABLE `t_interface_param_mapping`
ADD COLUMN `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型' AFTER `required`,
ADD COLUMN `default_value` varchar(255) DEFAULT NULL COMMENT '默认值' AFTER `data_type`,
ADD COLUMN `data_source_id` bigint(20) DEFAULT NULL COMMENT '数据源ID' AFTER `default_value`,
ADD COLUMN `table_name` varchar(100) DEFAULT NULL COMMENT '表名' AFTER `data_source_id`;