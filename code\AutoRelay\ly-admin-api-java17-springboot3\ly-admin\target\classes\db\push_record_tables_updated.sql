-- 定时推送任务去重功能数据库表结构（更新版本）
-- 包含动态时间参数支持

-- 推送记录表
CREATE TABLE `t_push_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `data_id` varchar(255) NOT NULL COMMENT '数据唯一标识',
  `data_hash` varchar(64) NOT NULL COMMENT '数据内容哈希值',
  `push_status` int NOT NULL DEFAULT '0' COMMENT '推送状态：0-待推送，1-推送成功，2-推送失败',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `error_message` text COMMENT '错误信息',
  `push_time` datetime COMMENT '推送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_data` (`task_id`, `data_id`),
  KEY `idx_task_status` (`task_id`, `push_status`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推送记录表';

-- 推送批次表（增加进度跟踪字段）
CREATE TABLE `t_push_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `total_count` int NOT NULL DEFAULT '0' COMMENT '总记录数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功数量',
  `failed_count` int NOT NULL DEFAULT '0' COMMENT '失败数量',
  `batch_status` int NOT NULL DEFAULT '0' COMMENT '批次状态：0-进行中，1-已完成',
  `current_page` int DEFAULT '1' COMMENT '当前处理页码',
  `total_pages` int DEFAULT '0' COMMENT '总页数',
  `time_window_start` datetime COMMENT '时间窗口开始时间',
  `time_window_end` datetime COMMENT '时间窗口结束时间',
  `progress_info` json COMMENT '进度信息（JSON格式）',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_task_status` (`task_id`, `batch_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推送批次表';

-- 推送配置表（优化后的统一推送逻辑）
CREATE TABLE `t_push_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `data_id_field` varchar(100) DEFAULT 'id' COMMENT '数据唯一标识字段名',
  `target_data_id_field` varchar(100) DEFAULT 'id' COMMENT '目标数据唯一标识字段名（查询结果中的别名）',
  `enable_deduplication` tinyint(1) DEFAULT '1' COMMENT '是否启用去重：0-否，1-是',
  `enable_data_change_detection` tinyint(1) DEFAULT '1' COMMENT '是否启用数据变更检测：0-否，1-是',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  `batch_size` int DEFAULT '1000' COMMENT '批次大小',

  `start_time` datetime DEFAULT NULL COMMENT '推送起始时间（可选，设置后只推送此时间之后的数据）',
  `update_time_field` varchar(100) DEFAULT 'update_time' COMMENT '数据更新时间字段名（用于排序和断点续传）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推送配置表';

-- 为现有的t_push_config表添加新字段的ALTER语句（如果表已存在）

-- ALTER TABLE `t_push_config` ADD COLUMN `max_time_window_minutes` int DEFAULT '60' COMMENT '最大时间窗口（分钟）';

-- 为现有的t_push_batch表添加新字段的ALTER语句（如果表已存在）
-- ALTER TABLE `t_push_batch` ADD COLUMN `current_page` int DEFAULT '1' COMMENT '当前处理页码';
-- ALTER TABLE `t_push_batch` ADD COLUMN `total_pages` int DEFAULT '0' COMMENT '总页数';
-- ALTER TABLE `t_push_batch` ADD COLUMN `time_window_start` datetime COMMENT '时间窗口开始时间';
-- ALTER TABLE `t_push_batch` ADD COLUMN `time_window_end` datetime COMMENT '时间窗口结束时间';
-- ALTER TABLE `t_push_batch` ADD COLUMN `progress_info` json COMMENT '进度信息（JSON格式）';

-- 创建索引
CREATE INDEX `idx_push_record_task_status` ON `t_push_record` (`task_id`, `push_status`);
CREATE INDEX `idx_push_record_batch` ON `t_push_record` (`batch_no`);
CREATE INDEX `idx_push_batch_task` ON `t_push_batch` (`task_id`);