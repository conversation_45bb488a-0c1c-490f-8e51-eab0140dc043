<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.base.module.support.helpdoc.dao.HelpDocDao">


    <!-- ================================== 帮助文档【主表 t_help_doc 】 ================================== -->
    <select id="queryAllHelpDocList" resultType="net.lingyue.ly.base.module.support.helpdoc.domain.vo.HelpDocVO">
        SELECT t_help_doc.*,
        t_help_doc_catalog.name as helpDocCatalogName
        FROM t_help_doc
        left join t_help_doc_catalog on t_help_doc_catalog.help_doc_catalog_id = t_help_doc.help_doc_catalog_id
    </select>

    <select id="query" resultType="net.lingyue.ly.base.module.support.helpdoc.domain.vo.HelpDocVO">
        SELECT
        t_help_doc.* ,
        t_help_doc_catalog.name as helpDocCatalogName
        FROM t_help_doc
        left join t_help_doc_catalog on t_help_doc_catalog.help_doc_catalog_id = t_help_doc.help_doc_catalog_id
        <where>
            <if test="query.helpDocCatalogId != null">
                AND t_help_doc.help_doc_catalog_id = #{query.helpDocCatalogId}
            </if>
            <if test="query.keywords != null and query.keywords !=''">
                AND ( INSTR(t_help_doc.title,#{query.keywords})
                OR INSTR(t_help_doc.author,#{query.keywords})
                )
            </if>
            <if test="query.createTimeBegin != null">
                AND DATE_FORMAT(t_help_doc.create_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y-%m-%d')
            </if>
            <if test="query.createTimeEnd != null">
                AND DATE_FORMAT(t_help_doc.create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y-%m-%d')
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY t_help_doc.sort ASC, t_help_doc.create_time DESC
        </if>
    </select>

    <update id="updateViewCount">
        update t_help_doc
        set page_view_count = page_view_count + #{pageViewCountIncrease},
        user_view_count = user_view_count + #{userViewCountIncrease}
        where help_doc_id = #{helpDocId}
    </update>

    <select id="queryHelpDocByCatalogId"
            resultType="net.lingyue.ly.base.module.support.helpdoc.domain.vo.HelpDocVO">
        select *
        from t_help_doc
        where help_doc_catalog_id = #{helpDocCatalogId}
    </select>

    <select id="queryHelpDocByRelationId"
            resultType="net.lingyue.ly.base.module.support.helpdoc.domain.vo.HelpDocVO">
        select t_help_doc.*
        from t_help_doc_relation
        left join t_help_doc on t_help_doc.help_doc_id = t_help_doc_relation.help_doc_id
        where t_help_doc_relation.relation_id = #{relationId}
    </select>

    <!-- ================================== 关联项目 【子表 关联关系 t_help_doc_relation 】 ================================== -->

    <insert id="insertRelation">
        insert into t_help_doc_relation
        (relation_id, relation_name, help_doc_id)
        values
        <foreach collection="relationList" separator="," item="item">
            ( #{item.relationId} ,#{item.relationName}, #{helpDocId} )
        </foreach>
    </insert>

    <delete id="deleteRelation">
        delete
        from t_help_doc_relation
        where help_doc_id = #{helpDocId}
    </delete>

    <select id="queryRelationByHelpDoc"
            resultType="net.lingyue.ly.base.module.support.helpdoc.domain.vo.HelpDocRelationVO">
        select *
        from t_help_doc_relation
        where help_doc_id = #{helpDocId}
    </select>

    <!-- ================================== 查看记录【子表 查看记录 t_help_doc_view_record】 ================================== -->
    <select id="viewRecordCount" resultType="java.lang.Long">
        select count(*)
        from t_help_doc_view_record
        where help_doc_id = #{helpDocId}
        and user_id = #{userId}
    </select>
    <insert id="insertViewRecord">
        insert into t_help_doc_view_record (help_doc_id, user_id,user_name, first_ip, first_user_agent, page_view_count)
        values (#{helpDocId}, #{userId},#{userName}, #{ip}, #{userAgent}, #{pageViewCount})
    </insert>
    <update id="updateViewRecord">
        update t_help_doc_view_record
        set page_view_count = page_view_count + 1,
        last_ip = #{ip},
        last_user_agent = #{userAgent}
        where help_doc_id = #{helpDocId}
        and user_id = #{userId}
    </update>
    <select id="queryViewRecordList"
            resultType="net.lingyue.ly.base.module.support.helpdoc.domain.vo.HelpDocViewRecordVO">
        select *
        from t_help_doc_view_record
        where
        help_doc_id = #{queryForm.helpDocId}
        <if test="queryForm.keywords != null and queryForm.keywords !=''">
            AND (
            INSTR(user_name,#{queryForm.keywords})
            OR INSTR(first_ip,#{queryForm.keywords})
            OR INSTR(first_user_agent,#{queryForm.keywords})
            OR INSTR(last_ip,#{queryForm.keywords})
            OR INSTR(last_user_agent,#{queryForm.keywords})
            )
        </if>
        <if test="queryForm.userId != null ">
            and user_id = #{queryForm.userId}
        </if>
        order by update_time desc,create_time desc
    </select>


</mapper>
