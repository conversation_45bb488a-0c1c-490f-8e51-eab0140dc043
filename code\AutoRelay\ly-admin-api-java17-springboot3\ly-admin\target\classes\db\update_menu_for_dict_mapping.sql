-- 更新菜单配置脚本：将第三方字典管理更新为数据字典映射管理，并添加独立的第三方系统管理
-- 创建时间：2024-12-28
-- 用途：更新现有的第三方字典管理菜单为数据字典映射管理，并添加独立的第三方系统管理

-- 1. 更新主菜单：将"第三方字典管理"更新为"数据字典映射管理"
UPDATE t_menu 
SET 
    menu_name = '数据字典映射管理',
    path = '/auto-relay/data-dict-mapping/list',
    component = '/auto-relay/data-dict-mapping/data-dict-mapping-list.vue',
    update_time = NOW()
WHERE menu_name = '第三方字典管理' 
AND deleted_flag = 0;

-- 2. 获取自动中继父菜单ID
SET @auto_relay_parent_id = NULL;
SELECT menu_id INTO @auto_relay_parent_id 
FROM t_menu 
WHERE menu_name = '自动中继' AND deleted_flag = 0 
LIMIT 1;

-- 3. 添加独立的第三方系统管理菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, sort, path, component, frame_flag, cache_flag, visible_flag, disabled_flag, perms_type, create_time, update_time)
VALUES ('第三方系统管理', 2, @auto_relay_parent_id, 21, '/auto-relay/third-party-system/list', '/auto-relay/third-party-system/third-party-system-list.vue', 0, 1, 1, 0, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    path = '/auto-relay/third-party-system/list',
    component = '/auto-relay/third-party-system/third-party-system-list.vue',
    update_time = NOW();

-- 4. 获取数据字典映射管理菜单ID
SET @dict_mapping_menu_id = NULL;
SELECT menu_id INTO @dict_mapping_menu_id 
FROM t_menu 
WHERE menu_name = '数据字典映射管理' AND deleted_flag = 0 
LIMIT 1;

-- 5. 删除数据字典映射管理的旧功能点权限
DELETE FROM t_menu 
WHERE parent_id = @dict_mapping_menu_id 
AND menu_type = 3;

-- 6. 添加数据字典映射管理的新功能点权限
INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms, perms_type, context_menu_id, create_time, update_time)
VALUES 
('查询', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:query', 1, @dict_mapping_menu_id, NOW(), NOW()),
('添加', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:add', 1, @dict_mapping_menu_id, NOW(), NOW()),
('更新', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:update', 1, @dict_mapping_menu_id, NOW(), NOW()),
('删除', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:delete', 1, @dict_mapping_menu_id, NOW(), NOW()),
('导出', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:export', 1, @dict_mapping_menu_id, NOW(), NOW()),
('导入', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:import', 1, @dict_mapping_menu_id, NOW(), NOW()),
('转换测试', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:convert', 1, @dict_mapping_menu_id, NOW(), NOW()),
('字典类型管理', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:dictTypeManage', 1, @dict_mapping_menu_id, NOW(), NOW());

-- 7. 获取第三方系统管理菜单ID
SET @third_party_system_menu_id = NULL;
SELECT menu_id INTO @third_party_system_menu_id 
FROM t_menu 
WHERE menu_name = '第三方系统管理' AND deleted_flag = 0 
LIMIT 1;

-- 8. 添加第三方系统管理的功能点权限
INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms, perms_type, context_menu_id, create_time, update_time)
VALUES 
('查询', 3, @third_party_system_menu_id, 0, 1, 1, 0, 'thirdPartySystem:query', 1, @third_party_system_menu_id, NOW(), NOW()),
('添加', 3, @third_party_system_menu_id, 0, 1, 1, 0, 'thirdPartySystem:add', 1, @third_party_system_menu_id, NOW(), NOW()),
('更新', 3, @third_party_system_menu_id, 0, 1, 1, 0, 'thirdPartySystem:update', 1, @third_party_system_menu_id, NOW(), NOW()),
('删除', 3, @third_party_system_menu_id, 0, 1, 1, 0, 'thirdPartySystem:delete', 1, @third_party_system_menu_id, NOW(), NOW());

-- 9. 删除其他相关的旧菜单（如果存在）
UPDATE t_menu 
SET deleted_flag = 1, update_time = NOW() 
WHERE menu_name IN ('第三方字典类型管理', '第三方字典值管理') 
AND deleted_flag = 0;

COMMIT; 