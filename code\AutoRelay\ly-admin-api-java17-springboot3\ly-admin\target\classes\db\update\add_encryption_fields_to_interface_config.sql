-- 为接口配置表添加加密和签名相关字段
ALTER TABLE t_interface_config 
ADD COLUMN enable_encryption TINYINT DEFAULT 0 COMMENT '是否启用加密',
ADD COLUMN encryption_algorithm VARCHAR(50) DEFAULT NULL COMMENT '加密算法',
ADD COLUMN encryption_key VARCHAR(255) DEFAULT NULL COMMENT '加密密钥',
ADD COLUMN encryption_mode VARCHAR(20) DEFAULT NULL COMMENT '加密模式',
ADD COLUMN encryption_iv VARCHAR(255) DEFAULT NULL COMMENT '初始向量(IV)',
ADD COLUMN encryption_padding VARCHAR(20) DEFAULT NULL COMMENT '填充方式',
ADD COLUMN enable_signature TINYINT DEFAULT 0 COMMENT '是否启用签名',
ADD COLUMN signature_algorithm VARCHAR(50) DEFAULT NULL COMMENT '签名算法',
ADD COLUMN signature_key VARCHAR(1024) DEFAULT NULL COMMENT '签名密钥',
ADD COLUMN signature_mode VARCHAR(20) DEFAULT NULL COMMENT '签名方式'; 