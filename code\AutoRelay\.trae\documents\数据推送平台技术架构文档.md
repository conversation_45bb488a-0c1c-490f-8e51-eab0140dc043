# 数据推送平台技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[Web管理界面] --> B[API网关层]
    B --> C[业务服务层]
    C --> D[数据访问层]
    C --> E[Redis缓存层]
    C --> F[任务调度层]
    
    subgraph "前端层"
        A
    end
    
    subgraph "服务层"
        B
        C
        F
    end
    
    subgraph "数据层"
        D --> G[(MySQL数据库)]
        E --> H[(Redis缓存)]
    end
    
    subgraph "外部系统"
        I[目标接口系统]
        J[源数据库系统]
    end
    
    C --> I
    C --> J
```

## 2. Technology Description

* Frontend: Vue3 + Element Plus + Vite + TypeScript

* Backend: Spring Boot 3 + Java 17 + MyBatis Plus

* Database: MySQL 8.0 + Redis 7.0

* Task Scheduler: Spring Task + Quartz

* Message Queue: Redis Stream (可选RabbitMQ)

* Monitoring: Micrometer + Prometheus + Grafana

## 3. Route definitions

| Route          | Purpose            |
| -------------- | ------------------ |
| /dashboard     | 主控制台，显示系统概览和关键指标   |
| /datasource    | 数据源管理页面，配置和管理数据库连接 |
| /interface     | 接口配置页面，管理目标接口和参数映射 |
| /task          | 任务管理页面，创建和管理推送任务   |
| /monitor       | 推送监控页面，实时监控和日志查看   |
| /deduplication | 去重配置页面，管理去重规则和唯一键  |
| /system        | 系统设置页面，用户和系统参数管理   |
| /login         | 用户登录页面             |

## 4. API definitions

### 4.1 Core API

**数据源管理相关**

```
POST /api/datasource/create
```

Request:

| Param Name | Param Type | isRequired | Description                    |
| ---------- | ---------- | ---------- | ------------------------------ |
| name       | string     | true       | 数据源名称                          |
| type       | string     | true       | 数据库类型(MySQL/Oracle/PostgreSQL) |
| host       | string     | true       | 数据库主机地址                        |
| port       | integer    | true       | 数据库端口                          |
| database   | string     | true       | 数据库名称                          |
| username   | string     | true       | 用户名                            |
| password   | string     | true       | 密码                             |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 操作是否成功      |
| data       | object     | 创建的数据源信息    |
| message    | string     | 响应消息        |

**接口配置相关**

```
POST /api/interface/create
```

Request:

| Param Name      | Param Type | isRequired | Description               |
| --------------- | ---------- | ---------- | ------------------------- |
| systemCode      | string     | true       | 系统编码                      |
| systemName      | string     | true       | 系统名称                      |
| interfaceName   | string     | true       | 接口名称                      |
| requestUrl      | string     | true       | 请求URL                     |
| requestMethod   | string     | true       | 请求方法(GET/POST/PUT/DELETE) |
| requestBodyType | string     | false      | 请求体类型(JSON/FORM/XML)      |
| headers         | array      | false      | 请求头配置                     |

**任务管理相关**

```
POST /api/task/create
```

Request:

| Param Name          | Param Type | isRequired | Description             |
| ------------------- | ---------- | ---------- | ----------------------- |
| taskName            | string     | true       | 任务名称                    |
| interfaceId         | long       | true       | 关联接口ID                  |
| dataSourceId        | long       | true       | 数据源ID                   |
| dataSql             | string     | true       | 数据查询SQL                 |
| triggerType         | string     | true       | 触发类型(CRON/FIXED\_DELAY) |
| triggerValue        | string     | true       | 触发值                     |
| batchSize           | integer    | false      | 批处理大小                   |
| enableDeduplication | boolean    | false      | 是否启用去重                  |

**推送监控相关**

```
GET /api/monitor/task/{taskId}/status
```

Response:

| Param Name       | Param Type | Description                 |
| ---------------- | ---------- | --------------------------- |
| taskId           | long       | 任务ID                        |
| status           | string     | 任务状态(RUNNING/STOPPED/ERROR) |
| lastExecuteTime  | datetime   | 最后执行时间                      |
| successCount     | long       | 成功推送数量                      |
| failedCount      | long       | 失败推送数量                      |
| avgExecutionTime | long       | 平均执行时间(毫秒)                  |

**去重管理相关**

```
POST /api/deduplication/config
```

Request:

| Param Name                | Param Type | isRequired | Description                     |
| ------------------------- | ---------- | ---------- | ------------------------------- |
| taskId                    | long       | true       | 任务ID                            |
| dataIdField               | string     | true       | 数据ID字段名                         |
| enableDeduplication       | boolean    | true       | 是否启用去重                          |
| enableDataChangeDetection | boolean    | false      | 是否启用数据变更检测                      |
| uniqueKeyStrategy         | string     | true       | 唯一键策略(ID\_ONLY/TIME\_ID\_COMBO) |
| maxRetryCount             | integer    | false      | 最大重试次数                          |

## 5. Server architecture diagram

```mermaid
graph TD
    A[Controller Layer] --> B[Service Layer]
    B --> C[Manager Layer]
    C --> D[DAO Layer]
    D --> E[(Database)]
    
    B --> F[Cache Service]
    F --> G[(Redis)]
    
    B --> H[Task Scheduler]
    H --> I[Push Executor]
    I --> J[External APIs]
    
    B --> K[Monitoring Service]
    K --> L[Metrics Collector]
    
    subgraph "业务服务层"
        B
        F
        H
        K
    end
    
    subgraph "数据访问层"
        C
        D
    end
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    INTERFACE_CONFIG ||--o{ INTERFACE_TASK : configures
    INTERFACE_TASK ||--o{ TASK_LOG : generates
    INTERFACE_TASK ||--o{ PUSH_CONFIG : has
    INTERFACE_TASK ||--o{ PUSH_BATCH : creates
    PUSH_BATCH ||--o{ PUSH_RECORD : contains
    DATA_SOURCE ||--o{ INTERFACE_TASK : provides_data
    
    INTERFACE_CONFIG {
        bigint id PK
        string system_code
        string system_name
        string interface_name
        string request_url
        string request_method
        text request_body_template
        datetime create_time
        datetime update_time
    }
    
    INTERFACE_TASK {
        bigint id PK
        string task_name
        bigint interface_id FK
        bigint data_source_id FK
        text data_sql
        string trigger_type
        string trigger_value
        boolean enabled_flag
        datetime last_execute_time
        datetime create_time
    }
    
    PUSH_CONFIG {
        bigint id PK
        bigint task_id FK
        string data_id_field
        boolean enable_deduplication
        boolean enable_data_change_detection
        string unique_key_strategy
        int max_retry_count
        int batch_size
    }
    
    PUSH_BATCH {
        bigint id PK
        bigint task_id FK
        string batch_no
        string batch_status
        int total_count
        int success_count
        int failed_count
        datetime start_time
        datetime end_time
    }
    
    PUSH_RECORD {
        bigint id PK
        bigint task_id FK
        bigint batch_id FK
        string data_id
        string data_hash
        string push_status
        text error_message
        int retry_count
        datetime push_time
    }
    
    DATA_SOURCE {
        bigint id PK
        string name
        string type
        string host
        int port
        string database_name
        string username
        string password
        datetime create_time
    }
```

### 6.2 Data Definition Language

**接口配置表 (t\_interface\_config)**

```sql
-- 接口配置表
CREATE TABLE t_interface_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    system_code VARCHAR(50) NOT NULL COMMENT '系统编码',
    system_name VARCHAR(100) NOT NULL COMMENT '系统名称',
    interface_name VARCHAR(100) NOT NULL COMMENT '接口名称',
    interface_type VARCHAR(20) NOT NULL DEFAULT 'REST' COMMENT '接口类型',
    request_url VARCHAR(500) NOT NULL COMMENT '请求URL',
    request_method VARCHAR(20) NOT NULL DEFAULT 'POST' COMMENT '请求方法',
    request_body_type VARCHAR(20) DEFAULT 'JSON' COMMENT '请求体类型',
    request_body_template TEXT COMMENT '请求体模板',
    enable_response_parse BOOLEAN DEFAULT FALSE COMMENT '是否启用响应解析',
    response_parse_config JSON COMMENT '响应解析配置',
    status TINYINT DEFAULT 1 COMMENT '状态 0停用 1启用',
    description VARCHAR(500) COMMENT '描述',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_flag TINYINT DEFAULT 0 COMMENT '删除标识'
);

-- 创建索引
CREATE INDEX idx_system_code ON t_interface_config(system_code);
CREATE INDEX idx_status ON t_interface_config(status);
```

**推送配置表 (t\_push\_config)**

```sql
-- 推送配置表
CREATE TABLE t_push_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    data_id_field VARCHAR(100) DEFAULT 'id' COMMENT '数据唯一标识字段名',
    target_data_id_field VARCHAR(100) COMMENT '目标数据ID字段名',
    enable_deduplication BOOLEAN DEFAULT TRUE COMMENT '是否启用去重',
    enable_data_change_detection BOOLEAN DEFAULT TRUE COMMENT '是否启用数据变更检测',
    unique_key_strategy VARCHAR(50) DEFAULT 'TIME_ID_COMBO' COMMENT '唯一键策略',
    max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
    batch_size INT DEFAULT 100 COMMENT '批处理大小',
    
    is_full_push BOOLEAN DEFAULT FALSE COMMENT '是否全量推送',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE UNIQUE INDEX uk_task_id ON t_push_config(task_id);
```

**推送批次表 (t\_push\_batch)**

```sql
-- 推送批次表
CREATE TABLE t_push_batch (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    batch_no VARCHAR(50) NOT NULL COMMENT '批次号',
    batch_status VARCHAR(20) DEFAULT 'PROCESSING' COMMENT '批次状态',
    total_count INT DEFAULT 0 COMMENT '总数据量',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    failed_count INT DEFAULT 0 COMMENT '失败数量',
    last_processed_id VARCHAR(100) COMMENT '最后处理的数据ID',
    progress_info JSON COMMENT '进度信息',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_task_id ON t_push_batch(task_id);
CREATE INDEX idx_batch_status ON t_push_batch(batch_status);
CREATE INDEX idx_start_time ON t_push_batch(start_time DESC);
```

**推送记录表 (t\_push\_record)**

```sql
-- 推送记录表（仅记录失败数据）
CREATE TABLE t_push_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    batch_id BIGINT COMMENT '批次ID',
    data_id VARCHAR(200) NOT NULL COMMENT '数据唯一标识',
    unique_key VARCHAR(300) COMMENT '唯一键(时间+ID组合)',
    data_hash VARCHAR(32) COMMENT '数据哈希值',
    push_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '推送状态',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    push_time DATETIME COMMENT '推送时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE UNIQUE INDEX uk_task_data_id ON t_push_record(task_id, data_id);
CREATE INDEX idx_unique_key ON t_push_record(unique_key);
CREATE INDEX idx_push_status ON t_push_record(push_status);
CREATE INDEX idx_retry_count ON t_push_record(retry_count);
```

**数据源表 (t\_data\_source)**

```sql
-- 数据源表
CREATE TABLE t_data_source (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    type VARCHAR(20) NOT NULL COMMENT '数据库类型',
    host VARCHAR(200) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(200) NOT NULL COMMENT '密码(加密存储)',
    connection_params JSON COMMENT '连接参数',
    status TINYINT DEFAULT 1 COMMENT '状态',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_flag TINYINT DEFAULT 0
);

-- 初始化数据
INSERT INTO t_data_source (name, type, host, port, database_name, username, password) VALUES
('默认MySQL数据源', 'MySQL', 'localhost', 3306, 'test_db', 'root', 'encrypted_password'),
('Oracle生产库', 'Oracle', '*************', 1521, 'ORCL', 'app_user', 'encrypted_password');
```

