<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.pushRecord.dao.PushBatchDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushBatchEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
        <result column="total_count" property="totalCount" jdbcType="INTEGER"/>
        <result column="success_count" property="successCount" jdbcType="INTEGER"/>
        <result column="failed_count" property="failedCount" jdbcType="INTEGER"/>
        <result column="batch_status" property="batchStatus" jdbcType="TINYINT"/>
        <result column="current_page" property="currentPage" jdbcType="INTEGER"/>
        <result column="total_pages" property="totalPages" jdbcType="INTEGER"/>
        <result column="time_window_start" property="timeWindowStart" jdbcType="TIMESTAMP"/>
        <result column="time_window_end" property="timeWindowEnd" jdbcType="TIMESTAMP"/>
        <result column="progress_info" property="progressInfo" jdbcType="LONGVARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, task_id, batch_no, total_count, success_count, failed_count,
        batch_status, current_page, total_pages, time_window_start, time_window_end,
        progress_info, start_time, end_time, create_time, update_time
    </sql>

    <!-- 根据批次号查询 -->
    <select id="selectByBatchNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_push_batch
        WHERE batch_no = #{batchNo,jdbcType=VARCHAR}
    </select>

    <!-- 根据任务ID查询批次列表 -->
    <select id="selectByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_push_batch
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>

    <!-- 更新批次统计信息 -->
    <update id="updateBatchStatistics">
        UPDATE t_push_batch
        SET success_count = #{successCount,jdbcType=INTEGER},
            failed_count = #{failedCount,jdbcType=INTEGER},
            batch_status = #{batchStatus,jdbcType=TINYINT},
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据任务ID删除推送批次 -->
    <delete id="deleteByTaskId" parameterType="java.lang.Long">
        DELETE FROM t_push_batch
        WHERE task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <!-- 根据任务ID查询最新完成的批次 -->
    <select id="selectLatestCompletedByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_push_batch
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        AND batch_status = 1
        ORDER BY end_time DESC
        LIMIT 1
    </select>

    <!-- 根据任务ID查询进行中的批次 -->
    <select id="selectInProgressBatchByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_push_batch
        WHERE task_id = #{taskId}
          AND batch_status = 0  -- 0表示进行中
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper>
