<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.base.module.support.codegenerator.dao.CodeGeneratorDao">

    <select id="countByTableName" resultType="Long">
        select count(*)
        from information_schema.tables
        where table_schema = (select database())
        and table_name = #{tableName}
    </select>

    <select id="selectTableColumn"
            resultType="net.lingyue.ly.base.module.support.codegenerator.domain.vo.TableColumnVO">
        select *
        from information_schema.columns
        where table_schema = (select database())
        and table_name = #{tableName}
        order by ordinal_position
    </select>

    <select id="queryTableList"
            resultType="net.lingyue.ly.base.module.support.codegenerator.domain.vo.TableVO">
        select
        tables.table_name,
        tables.table_comment,
        tables.create_time,
        tables.update_time,
        t_code_generator_config.update_time configTime
        from information_schema.tables tables
        left join t_code_generator_config on tables.table_name = t_code_generator_config.table_name
        where tables.table_schema = (select database())
        <if test="queryForm.tableNameKeywords != null and queryForm.tableNameKeywords != ''">
            AND INSTR(tables.table_name,#{queryForm.tableNameKeywords})
        </if>
    </select>
</mapper>
