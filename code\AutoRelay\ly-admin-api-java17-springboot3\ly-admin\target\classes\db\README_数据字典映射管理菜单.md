# 数据字典映射管理菜单初始化说明

## 概述
本文档说明如何为新的数据字典映射管理功能添加菜单项，让用户能通过系统菜单正常访问该功能。

## 前置条件
- 已完成数据字典映射管理的后端重构
- 已完成前端页面重构
- 系统中已存在菜单管理功能

## 执行步骤

### 1. 执行菜单初始化脚本
在数据库中执行 `data_dict_mapping_menu.sql` 脚本，该脚本将：

- 创建或查找"自动中继"父菜单
- 添加"数据字典映射管理"菜单项
- 添加相关的功能点权限
- 删除旧的第三方字典管理菜单

### 2. 菜单结构
执行后将创建以下菜单结构：

```
自动中继
└── 数据字典映射管理
    ├── 查询
    ├── 添加
    ├── 更新
    ├── 删除
    ├── 导出
    ├── 导入
    ├── 转换测试
    ├── 系统管理
    └── 字典类型管理
```

### 3. 路由配置
菜单对应的路由信息：
- **路径**: `/auto-relay/data-dict-mapping/list`
- **组件**: `/auto-relay/data-dict-mapping/data-dict-mapping-list.vue`
- **缓存**: 启用
- **显示**: 启用

### 4. 权限配置
系统将创建以下权限点：
- `dataDictMapping:query` - 查询权限
- `dataDictMapping:add` - 添加权限
- `dataDictMapping:update` - 更新权限
- `dataDictMapping:delete` - 删除权限
- `dataDictMapping:export` - 导出权限
- `dataDictMapping:import` - 导入权限
- `dataDictMapping:convert` - 转换测试权限
- `dataDictMapping:systemManage` - 系统管理权限
- `dataDictMapping:dictTypeManage` - 字典类型管理权限

### 5. 角色授权
执行脚本后，需要在系统的角色管理中为相应的角色分配新菜单的访问权限。

## 验证
脚本执行成功后：
1. 在系统菜单管理中应能看到新的菜单项
2. 在角色管理中应能看到新的权限点
3. 具有权限的用户应能通过菜单访问数据字典映射管理功能

## 注意事项
- 执行前请备份菜单相关数据
- 确保数据库连接正常
- 如果系统中已存在相关菜单，脚本会进行相应处理
- 旧的第三方字典管理菜单将被标记为删除状态 