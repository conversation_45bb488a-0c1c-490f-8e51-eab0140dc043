# Redis断点信息完整保存方案

## 问题分析

您提出的问题非常关键：**`BreakpointInfo` 的时间窗口数据没有保存到Redis中**。

### 原有问题

**只保存了基础断点信息：**
- `lastTime` - 最后处理时间
- `lastId` - 最后处理ID

**缺少了关键的推送周期信息：**
- `currentWindowStart` - 当前窗口开始时间
- `currentWindowEnd` - 当前窗口结束时间  
- `windowCompleted` - 窗口是否完成

这导致重启后无法准确恢复推送周期的状态。

## 完整解决方案

### 1. Redis键设计

```java
// 原有的键
private static final String MAX_ID_KEY_PREFIX = "push:maxid:";        // 最大ID
private static final String LAST_TIME_KEY_PREFIX = "push:lasttime:";  // 最后时间

// 🔥 新增的键
private static final String TIME_WINDOW_KEY_PREFIX = "push:window:";   // 时间窗口信息
```

### 2. Redis数据结构

#### 基础断点信息（原有）
```
push:maxid:1 = "12345"
push:lasttime:1 = "2025-01-01 10:30:00"
```

#### 时间窗口信息（新增）
```
push:window:1 = {
  "windowStart": "2025-01-01 10:00:00",
  "windowEnd": "2025-01-01 11:00:00",
  "windowCompleted": false,
  "lastUpdateTime": "2025-01-01 10:30:15"
}
```

### 3. 保存逻辑修复

#### 修复前的保存逻辑
```java
private void saveBreakpointToRedis(Long taskId, BreakpointInfo breakpoint) {
    // 只保存基础信息
    if (breakpoint.getLastTime() != null) {
        redisDeduplicationService.saveLastProcessedTime(taskId, breakpoint.getLastTime());
    }
    if (breakpoint.getLastId() != null) {
        redisDeduplicationService.saveMaxProcessedId(taskId, breakpoint.getLastId());
    }
    // ❌ 时间窗口信息丢失
}
```

#### 修复后的保存逻辑
```java
private void saveBreakpointToRedis(Long taskId, BreakpointInfo breakpoint) {
    // 保存基础断点信息
    if (breakpoint.getLastTime() != null) {
        redisDeduplicationService.saveLastProcessedTime(taskId, breakpoint.getLastTime());
    }
    if (breakpoint.getLastId() != null) {
        redisDeduplicationService.saveMaxProcessedId(taskId, breakpoint.getLastId());
    }
    
    // 🔥 关键修复：保存完整的时间窗口信息
    saveTimeWindowInfoToRedis(taskId, breakpoint);
}

private void saveTimeWindowInfoToRedis(Long taskId, BreakpointInfo breakpoint) {
    Map<String, Object> windowInfo = new HashMap<>();
    
    if (breakpoint.getCurrentWindowStart() != null) {
        windowInfo.put("windowStart", breakpoint.getCurrentWindowStart()
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }
    if (breakpoint.getCurrentWindowEnd() != null) {
        windowInfo.put("windowEnd", breakpoint.getCurrentWindowEnd()
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }
    windowInfo.put("windowCompleted", breakpoint.isWindowCompleted());
    windowInfo.put("lastUpdateTime", LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    
    String windowInfoJson = objectMapper.writeValueAsString(windowInfo);
    redisDeduplicationService.saveTimeWindowInfo(taskId, windowInfoJson);
}
```

### 4. 恢复逻辑修复

#### 修复前的恢复逻辑
```java
private BreakpointInfo getBreakpointFromRedis(Long taskId) {
    String lastTime = redisDeduplicationService.getLastProcessedTime(taskId);
    String lastId = redisDeduplicationService.getMaxProcessedId(taskId);
    
    if (lastTime != null || (lastId != null && !"0".equals(lastId))) {
        return new BreakpointInfo(lastTime, lastId);
        // ❌ 时间窗口信息丢失，需要重新计算
    }
    return null;
}
```

#### 修复后的恢复逻辑
```java
private BreakpointInfo getBreakpointFromRedis(Long taskId) {
    // 获取基础断点信息
    String lastTime = redisDeduplicationService.getLastProcessedTime(taskId);
    String lastId = redisDeduplicationService.getMaxProcessedId(taskId);
    
    // 获取时间窗口信息
    String windowInfoJson = redisDeduplicationService.getTimeWindowInfo(taskId);
    
    if (lastTime != null || (lastId != null && !"0".equals(lastId)) || windowInfoJson != null) {
        BreakpointInfo breakpoint = new BreakpointInfo(lastTime, lastId);
        
        // 🔥 关键修复：恢复时间窗口状态
        if (windowInfoJson != null && !windowInfoJson.isEmpty()) {
            restoreTimeWindowInfo(breakpoint, windowInfoJson);
        }
        
        return breakpoint;
    }
    return null;
}

private void restoreTimeWindowInfo(BreakpointInfo breakpoint, String windowInfoJson) {
    Map<String, Object> windowInfo = objectMapper.readValue(windowInfoJson, Map.class);
    
    // 恢复窗口开始时间
    String windowStartStr = (String) windowInfo.get("windowStart");
    if (windowStartStr != null) {
        LocalDateTime windowStart = LocalDateTime.parse(windowStartStr, 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        breakpoint.setCurrentWindowStart(windowStart);
    }
    
    // 恢复窗口结束时间
    String windowEndStr = (String) windowInfo.get("windowEnd");
    if (windowEndStr != null) {
        LocalDateTime windowEnd = LocalDateTime.parse(windowEndStr, 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        breakpoint.setCurrentWindowEnd(windowEnd);
    }
    
    // 恢复窗口完成状态
    Boolean windowCompleted = (Boolean) windowInfo.get("windowCompleted");
    if (windowCompleted != null) {
        breakpoint.setWindowCompleted(windowCompleted);
    }
}
```

## 实际应用场景

### 场景1：正常推送周期

```
第一次执行：
1. 计算时间窗口：2025-01-01 10:00:00 ~ 11:00:00
2. 查询500条数据，处理完成
3. 保存到Redis：
   - push:lasttime:1 = "2025-01-01 10:30:00"
   - push:maxid:1 = "12345"
   - push:window:1 = {"windowStart":"2025-01-01 10:00:00","windowEnd":"2025-01-01 11:00:00","windowCompleted":false}

第二次执行（30秒后）：
1. 从Redis恢复：窗口 10:00:00 ~ 11:00:00，未完成
2. 从断点 10:30:00, ID=12345 继续查询
3. 查询到200条数据（小于500），窗口完成
4. 更新Redis：windowCompleted = true
```

### 场景2：服务重启恢复

```
重启前状态：
- push:window:1 = {"windowStart":"2025-01-01 10:00:00","windowEnd":"2025-01-01 11:00:00","windowCompleted":false}
- push:lasttime:1 = "2025-01-01 10:30:00"
- push:maxid:1 = "12345"

重启后恢复：
1. 从Redis完整恢复断点信息
2. 知道当前窗口：10:00:00 ~ 11:00:00，未完成
3. 从断点 10:30:00, ID=12345 继续处理
4. 无需重新计算时间窗口，确保连续性
```

### 场景3：窗口切换

```
当前窗口完成后：
1. windowCompleted = true
2. 准备下一窗口：11:00:00 ~ 12:00:00
3. 更新Redis：
   - push:window:1 = {"windowStart":"2025-01-01 11:00:00","windowEnd":"2025-01-01 12:00:00","windowCompleted":false}
   - 断点信息保持上次的最大值
```

## 新增的RedisDeduplicationService方法

```java
/**
 * 保存时间窗口信息
 */
public void saveTimeWindowInfo(Long taskId, String windowInfoJson) {
    String key = generateRedisKey(TIME_WINDOW_KEY_PREFIX, taskId.toString());
    redisService.set(key, windowInfoJson, DEFAULT_EXPIRE_SECONDS);
}

/**
 * 获取时间窗口信息
 */
public String getTimeWindowInfo(Long taskId) {
    String key = generateRedisKey(TIME_WINDOW_KEY_PREFIX, taskId.toString());
    return redisService.get(key);
}
```

## 预期效果

修复后的完整方案能够：

1. ✅ **完整保存推送周期状态**：窗口开始时间、结束时间、完成状态
2. ✅ **准确恢复推送进度**：重启后能从正确的窗口和断点继续
3. ✅ **避免重复计算**：无需重新计算时间窗口，提高效率
4. ✅ **确保数据连续性**：窗口切换和断点续传都能正确处理
5. ✅ **支持复杂场景**：服务重启、异常恢复、窗口切换等

这样就彻底解决了"第一次推送完成后没有在Redis记录最大时间"以及时间窗口信息丢失的问题。
