<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.dataDictMapping.dao.DataDictMappingDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.dataDictMapping.domain.DataDictMappingEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="system_code" property="systemCode" jdbcType="VARCHAR"/>
        <result column="dict_type_code" property="dictTypeCode" jdbcType="VARCHAR"/>
        <result column="dict_type_name" property="dictTypeName" jdbcType="VARCHAR"/>
        <result column="source_value" property="sourceValue" jdbcType="VARCHAR"/>
        <result column="source_name" property="sourceName" jdbcType="VARCHAR"/>
        <result column="target_value" property="targetValue" jdbcType="VARCHAR"/>
        <result column="target_name" property="targetName" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="deleted_flag" property="deletedFlag" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, system_code, dict_type_code, dict_type_name, source_value, source_name,
        target_value, target_name, sort, remark, deleted_flag, create_time, update_time
    </sql>

    <!-- 根据系统编码和字典类型编码查询映射列表 -->
    <select id="selectBySystemAndDictType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_dict_mapping
        WHERE deleted_flag = 0
        AND system_code = #{systemCode}
        AND dict_type_code = #{dictTypeCode}
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据系统编码、字典类型编码和源值查询映射 -->
    <select id="selectBySourceValue" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_dict_mapping
        WHERE deleted_flag = 0
        AND system_code = #{systemCode}
        AND dict_type_code = #{dictTypeCode}
        AND source_value = #{sourceValue}
        LIMIT 1
    </select>

    <!-- 批量插入映射数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_data_dict_mapping (
        system_code, dict_type_code, dict_type_name, source_value, source_name,
        target_value, target_name, sort, remark, deleted_flag, create_time, update_time
        ) VALUES
        <foreach collection="mappings" item="item" separator=",">
            (
            #{item.systemCode},
            #{item.dictTypeCode},
            #{item.dictTypeName},
            #{item.sourceValue},
            #{item.sourceName},
            #{item.targetValue},
            #{item.targetName},
            #{item.sort},
            #{item.remark},
            #{item.deletedFlag},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据系统编码和字典类型编码删除映射 -->
    <update id="deleteBySystemAndDictType">
        UPDATE t_data_dict_mapping
        SET deleted_flag = 1, update_time = NOW()
        WHERE system_code = #{systemCode}
        AND dict_type_code = #{dictTypeCode}
        AND deleted_flag = 0
    </update>

</mapper> 