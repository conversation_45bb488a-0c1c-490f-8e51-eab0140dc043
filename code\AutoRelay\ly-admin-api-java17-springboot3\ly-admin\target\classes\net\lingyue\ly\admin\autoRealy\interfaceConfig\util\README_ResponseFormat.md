# 响应数据格式化工具使用说明

## 概述

`ResponseFormatUtil` 是一个用于格式化接口响应数据的工具类，主要解决以下问题：

1. **WebService响应中的HTML转义字符问题**：自动处理 `&quot;`、`&lt;`、`&gt;` 等转义字符
2. **JSON响应美化**：自动格式化JSON响应，提高可读性
3. **统一响应处理**：为REST和WebService接口提供统一的响应格式化逻辑

## 主要功能

### 1. 格式化响应数据

```java
// 格式化REST接口响应
String formattedResponse = ResponseFormatUtil.formatResponse(responseBody, "REST");

// 格式化WebService接口响应
String formattedResponse = ResponseFormatUtil.formatResponse(responseBody, "WEBSERVICE");
```

### 2. HTML实体反转义

```java
// 处理包含HTML转义字符的文本
String unescapedText = ResponseFormatUtil.unescapeHtmlEntities("&quot;Hello World&quot;");
// 结果: "Hello World"
```

### 3. JSON美化

```java
// 美化JSON格式
String beautifiedJson = ResponseFormatUtil.beautifyJsonIfPossible(jsonString);
```

### 4. 响应格式检测

```java
// 检测响应数据格式
String format = ResponseFormatUtil.detectResponseFormat(responseBody);
// 返回: "JSON", "XML", 或 "TEXT"
```

### 5. 获取完整响应数据

```java
// 获取包含原始和格式化版本的响应数据
ResponseFormatUtil.ResponseData data = ResponseFormatUtil.getFormattedResponseData(responseBody, "WEBSERVICE");
String original = data.getOriginal();     // 原始响应
String formatted = data.getFormatted();   // 格式化后的响应
String format = data.getFormat();         // 数据格式类型
```

## 支持的HTML实体

工具类支持以下HTML实体的反转义：

- `&quot;` → `"`
- `&apos;` → `'`
- `&lt;` → `<`
- `&gt;` → `>`
- `&#x27;` → `'`
- `&#x2F;` → `/`
- `&#x60;` → `` ` ``
- `&#x3D;` → `=`
- `&amp;` → `&` (最后处理)

## 使用场景

### WebService响应处理

当WebService返回包含转义字符的响应时：

```xml
<!-- 原始响应 -->
<response>&quot;{&quot;code&quot;:200,&quot;message&quot;:&quot;success&quot;}&quot;</response>

<!-- 格式化后 -->
<response>{
  "code": 200,
  "message": "success"
}</response>
```

### REST接口响应美化

```json
// 原始响应
{"code":200,"data":{"id":1,"name":"test"},"message":"success"}

// 格式化后
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "test"
  },
  "message": "success"
}
```

## 集成说明

该工具已集成到 `InterfaceInvokeService` 中：

- `invokeRestWithRequestBody` 方法自动使用 "REST" 模式格式化响应
- `invokeWebServiceWithRequestBody` 方法自动使用 "WEBSERVICE" 模式格式化响应

## 错误处理

工具类采用安全的错误处理策略：

- 如果格式化失败，返回原始数据
- 记录警告日志，不影响正常业务流程
- 对于无效的JSON，尝试提取并格式化其中的JSON片段

## 性能考虑

- 使用高效的字符串替换操作
- 避免不必要的正则表达式匹配
- 对于大型响应数据，建议在必要时才进行格式化