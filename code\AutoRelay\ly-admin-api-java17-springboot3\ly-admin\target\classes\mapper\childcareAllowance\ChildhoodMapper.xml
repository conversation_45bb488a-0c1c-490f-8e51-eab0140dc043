<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.childcareAllowance.dao.ChildhoodDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.childcareAllowance.domain.vo.ChildhoodVO">
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="id_card" property="id_card" jdbcType="VARCHAR"/>
        <result column="child_order" property="child_order" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 查询数据库驱动列表 -->
    <select id="getChildOrder" resultMap="BaseResultMap">
        SELECT * FROM ( SELECT
        info.PFPTABLE002CB01 AS name,
        info.PFPTABLE002CB00 AS id_card,
        dict.BASETABLE013CB02 AS child_order,
        DECODE(info.PFPTABLE002CB02, '5', '男', '6', '女', '4', '未知的性别','7', '未说明的性别') AS child_gender,
        mother.PFPTABLE002CB00 AS mother_id_card,
        mother.PFPTABLE002CB01 AS mother_name,
        mother.PFPTABLE002CB30 AS mather_id_type
        FROM
        PFPTABLE002 info
        LEFT JOIN
        PFPTABLE005 ch ON info.PFPTABLE002CA00 = ch.PFPTABLE005CB00
        LEFT JOIN
        PFPTABLE002 mother ON ch.PFPTABLE005CA00 = mother.PFPTABLE002CA00
        LEFT JOIN (select * from BASETABLE013 WHERE BASETABLE013CB01 = 33) dict ON dict.BASETABLE013ID = ch.PFPTABLE005CB03
        <where>
            <if test="name != null and name != ''">
                AND info.PFPTABLE002CB01 = #{name}
            </if>
            <if test="id_card != null and id_card != ''">
                AND info.PFPTABLE002CB00 = #{id_card}
            </if>
            <if test="gender != null and gender != ''">
                AND info.PFPTABLE002CB02 = #{gender}
            </if>
            <if test="birthday != null and birthday != ''">
                AND info.PFPTABLE002CB13 = TO_DATE(#{birthday}, 'YYYY-MM-DD')
            </if>
            <if test="mather_name != null and mather_name != ''">
                AND mother.PFPTABLE002CB01  = #{mather_name}
            </if>
<!--            <if test="mather_id_type != null and mather_id_type != ''">-->
<!--                AND mother.PFPTABLE002CB30 = #{mather_id_type}-->
<!--            </if>-->
            <if test="mather_id_card != null and mather_id_card != ''">
                AND mother.PFPTABLE002CB00 = #{mather_id_card}
            </if>
        </where>
        ORDER BY ch.PFPTABLE005CC02 DESC)  icmd
        WHERE ROWNUM = 1
    </select>

</mapper>
