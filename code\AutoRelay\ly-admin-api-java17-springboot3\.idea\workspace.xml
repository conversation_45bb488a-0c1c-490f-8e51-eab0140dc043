<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="108ec7f3-874f-401a-85f2-c2e9b4b7eac7" name="更改" comment="refactor: 优化数据库配置文件格式&#10;&#10;- 移除了多余的空行和密码字段后的多余空格&#10;- 注释掉的数据库连接信息保持不变">
      <change beforePath="$PROJECT_DIR$/ly-admin/README_真正的分批处理方案.md" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/README_真正的分批处理方案.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceConfig/service/InterfaceConfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceConfig/service/InterfaceConfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceTask/service/InterfaceTaskService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceTask/service/InterfaceTaskService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/pushRecord/domain/entity/PushConfigEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/pushRecord/domain/entity/PushConfigEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/pushRecord/service/BatchProcessingService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/pushRecord/service/BatchProcessingService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/pushRecord/service/PushRecordService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/pushRecord/service/PushRecordService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/util/SqlExecutorUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/util/SqlExecutorUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/resources/db/push_record_tables_updated.sql" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/resources/db/push_record_tables_updated.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/main/resources/mapper/autoRealy/PushConfigMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/src/main/resources/mapper/autoRealy/PushConfigMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/test/java/net/lingyue/ly/admin/autoRealy/pushRecord/service/BatchProcessingServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/src/test/java/net/lingyue/ly/admin/autoRealy/pushRecord/service/PushResponseParseServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ly-admin/定时任务诊断脚本.sql" beforeDir="false" afterPath="$PROJECT_DIR$/ly-admin/定时任务诊断脚本.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="true" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="AutoRelay01" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/consoles/db/05477873-a91d-4b23-a77e-58ddde119a3a/console_16.sql" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\software\apache-maven-3.9.9" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\software\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2tO1145qTG2jMKGkU8LF8wvSK3f" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.DatabaseUtil.executor&quot;: &quot;Run&quot;,
    &quot;Maven.ly-admin [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ly-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ly-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ly-parent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ly-parent [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.AdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;Comma-separated (CSV)_id&quot;,
    &quot;git-widget-placeholder&quot;: &quot;AutoRelay0902&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/code/AutoRelay/ly-admin-api-java17-springboot3/ly-admin/src/main/resources/mapper/childcareAllowance&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.31609195&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.Confirmation&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.DatabaseUtil.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.DruidConnectionTester.executor&quot;: &quot;Run&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;,
      &quot;oracle&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\ly-admin\src\main\resources\mapper\childcareAllowance" />
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\ly-admin\src\main\java\net\lingyue\ly\admin\childcareAllowance" />
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\.lingma\rules" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\ly-admin\src\main\resources\mapper\autoRealy" />
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\ly-admin\src\main\resources\db" />
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\ly-admin\src\main\java\net\lingyue\ly\admin\autoRealy" />
      <recent name="E:\code\AutoRelay\ly-admin-api-java17-springboot3\ly-admin\src\main\resources\mapper\autoRealy\dataSourceManage" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="net.lingyue.ly.base.common.domain" />
      <recent name="net.lingyue.ly.admin.childcareAllowance.domain.vo" />
      <recent name="net.lingyue.ly.admin.childcareAllowance.domain.dto" />
      <recent name="net.lingyue.ly.admin.childcareAllowance.domain" />
      <recent name="net.lingyue.ly.admin.childcareAllowance.manager" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.AdminApplication">
    <configuration name="DatabaseUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.lingyue.ly.admin.autoRealy.DatabaseUtil" />
      <module name="ly-admin" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.lingyue.ly.admin.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DatabaseUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.lingyue.ly.admin.autoRealy.DatabaseUtil" />
      <module name="ly-admin" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.lingyue.ly.admin.autoRealy.DatabaseUtil" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DruidConnectionTester" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.lingyue.ly.admin.util.DruidConnectionTester" />
      <module name="ly-admin" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.lingyue.ly.admin.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ly-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="net.lingyue.ly.admin.AdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.DatabaseUtil" />
        <item itemvalue="应用程序.DruidConnectionTester" />
        <item itemvalue="应用程序.DatabaseUtil" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="108ec7f3-874f-401a-85f2-c2e9b4b7eac7" name="更改" comment="" />
      <created>1740208088590</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740208088590</updated>
      <workItem from="1740208089829" duration="367000" />
      <workItem from="1740208468900" duration="105000" />
      <workItem from="1740209344905" duration="4019000" />
      <workItem from="1740368812214" duration="60000" />
      <workItem from="1740371493637" duration="154000" />
      <workItem from="1740644586849" duration="1643000" />
      <workItem from="1742372773362" duration="3482000" />
      <workItem from="1742432342908" duration="16117000" />
      <workItem from="1742518267350" duration="9786000" />
      <workItem from="1742788741639" duration="8960000" />
      <workItem from="1742808613381" duration="122000" />
      <workItem from="1742811937357" duration="1245000" />
      <workItem from="1742862732123" duration="23218000" />
      <workItem from="1742956965750" duration="25199000" />
      <workItem from="1743035397638" duration="18570000" />
      <workItem from="1743131975651" duration="20714000" />
      <workItem from="1743383482428" duration="1743000" />
      <workItem from="1743557759319" duration="603000" />
      <workItem from="1743563556759" duration="8509000" />
      <workItem from="1743587844627" duration="122000" />
      <workItem from="1743640670855" duration="5991000" />
      <workItem from="1743652264974" duration="28000" />
      <workItem from="1743661095620" duration="9984000" />
      <workItem from="1743770436559" duration="4149000" />
      <workItem from="1743817748502" duration="5852000" />
      <workItem from="1744072536871" duration="4316000" />
      <workItem from="1744090367958" duration="15537000" />
      <workItem from="1744159827894" duration="7356000" />
      <workItem from="1744168702041" duration="17497000" />
      <workItem from="1744245746140" duration="20343000" />
      <workItem from="1744281727376" duration="146000" />
      <workItem from="1744336521999" duration="6079000" />
      <workItem from="1744420793776" duration="608000" />
      <workItem from="1744590157757" duration="21118000" />
      <workItem from="1744625926448" duration="2870000" />
      <workItem from="1744677166683" duration="1307000" />
      <workItem from="1744678997884" duration="514000" />
      <workItem from="1744679555300" duration="26059000" />
      <workItem from="1744767789660" duration="16573000" />
      <workItem from="1744851348378" duration="8769000" />
      <workItem from="1744862109442" duration="20537000" />
      <workItem from="1744898983293" duration="1428000" />
      <workItem from="1744937796134" duration="11338000" />
      <workItem from="1744955116778" duration="12121000" />
      <workItem from="1745197110397" duration="2243000" />
      <workItem from="1745206321055" duration="2675000" />
      <workItem from="1745393901248" duration="1628000" />
      <workItem from="1747973297900" duration="2609000" />
      <workItem from="1748170179874" duration="824000" />
      <workItem from="1748176262329" duration="7320000" />
      <workItem from="1748221006047" duration="35796000" />
      <workItem from="1748334992066" duration="17957000" />
      <workItem from="1748395024477" duration="3551000" />
      <workItem from="1748952486175" duration="3124000" />
      <workItem from="1749010729175" duration="7498000" />
      <workItem from="1749045613777" duration="946000" />
      <workItem from="1749090432628" duration="4937000" />
      <workItem from="1749124358061" duration="3355000" />
      <workItem from="1749177376568" duration="2754000" />
      <workItem from="1749349082443" duration="102000" />
      <workItem from="1751263955298" duration="3190000" />
      <workItem from="1751338790410" duration="7670000" />
      <workItem from="1751505479150" duration="310000" />
      <workItem from="1751507620671" duration="8587000" />
      <workItem from="1751550475832" duration="16754000" />
      <workItem from="1751598342503" duration="20832000" />
      <workItem from="1751811553034" duration="922000" />
      <workItem from="1751850740192" duration="262000" />
      <workItem from="1751851249176" duration="45000" />
      <workItem from="1751863281378" duration="4330000" />
      <workItem from="1752719363997" duration="9665000" />
      <workItem from="1752806951124" duration="9436000" />
      <workItem from="1752892040977" duration="27585000" />
      <workItem from="1753063983010" duration="604000" />
      <workItem from="1753068040547" duration="12011000" />
      <workItem from="1753147539869" duration="3878000" />
      <workItem from="1753153688465" duration="9637000" />
      <workItem from="1753178231816" duration="7689000" />
      <workItem from="1753191894269" duration="4663000" />
      <workItem from="1753324004417" duration="5929000" />
      <workItem from="1753341926730" duration="12547000" />
      <workItem from="1753411839297" duration="1688000" />
      <workItem from="1753413996879" duration="22106000" />
      <workItem from="1753503494745" duration="20993000" />
      <workItem from="1753581715902" duration="53312000" />
      <workItem from="1753774626707" duration="11000" />
      <workItem from="1753945641440" duration="24049000" />
      <workItem from="1753978716960" duration="794000" />
      <workItem from="1754470412697" duration="7260000" />
      <workItem from="1754526928357" duration="37709000" />
      <workItem from="1754616613812" duration="22093000" />
      <workItem from="1754798885923" duration="17009000" />
      <workItem from="1756697328640" duration="10433000" />
      <workItem from="1756744009195" duration="7537000" />
      <workItem from="1756793945478" duration="8853000" />
      <workItem from="1756868454886" duration="11864000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1740208562271</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1740208562271</updated>
    </task>
    <task id="LOCAL-00002" summary="init">
      <option name="closed" value="true" />
      <created>1740217053421</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1740217053421</updated>
    </task>
    <task id="LOCAL-00003" summary="init">
      <option name="closed" value="true" />
      <created>1740217095003</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1740217095003</updated>
    </task>
    <task id="LOCAL-00004" summary="init">
      <option name="closed" value="true" />
      <created>1742465544918</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1742465544918</updated>
    </task>
    <task id="LOCAL-00005" summary="feat(auto-relay): 添加数据源管理功能&#10;&#10;- 新增数据源和数据库驱动相关表结构&#10;- 实现数据源管理的 CRUD接口和服务&#10;- 添加数据源连接测试和设置默认数据源功能&#10;- 集成数据追踪服务，记录数据源变更">
      <option name="closed" value="true" />
      <created>1742813494992</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1742813494992</updated>
    </task>
    <task id="LOCAL-00006" summary="feat(auto-relay): 添加数据源管理功能&#10;&#10;- 新增数据源和数据库驱动相关表结构&#10;- 实现数据源管理的 CRUD接口和服务&#10;- 添加数据源连接测试和设置默认数据源功能&#10;- 集成数据追踪服务，记录数据源变更">
      <option name="closed" value="true" />
      <created>1742813513894</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1742813513894</updated>
    </task>
    <task id="LOCAL-00007" summary="feat(dataSource): 动态连接数据库并获取表信息&#10;&#10;- 新增接口和实现类，支持动态连接数据库并获取表名列表&#10;- 优化数据库驱动上传逻辑，确保驱动文件正确保存&#10;- 添加必要的配置项，支持文件上传&#10;- 更新.gitignore文件，忽略编译后的目标文件">
      <option name="closed" value="true" />
      <created>1742897283969</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1742897283969</updated>
    </task>
    <task id="LOCAL-00008" summary="feat(autoRealy): 添加数据库连接和查询工具类&#10;&#10;- 新增 DatabaseTestUtil 和 DatabaseUtil 类，用于获取表名和列名- 添加 DriverShim 类，用于包装数据库驱动&#10;- 新增 DruidConfig 类，配置 Druid 数据源- 添加 DruidConnectionTester 类，用于测试数据库连接">
      <option name="closed" value="true" />
      <created>1742985775852</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1742985775852</updated>
    </task>
    <task id="LOCAL-00009" summary="refactor(autoRealy): 重构数据源管理功能&#10;&#10;- 将 DataSource 类重命名为 AutoDataSource&#10;- 重构 DatabaseUtil 类，支持多种数据库类型&#10;- 更新数据源管理相关的控制器、服务、DAO 和 XML 文件&#10;- 将 DruidConfig 类移动到 util 包，并改为动态初始化数据源">
      <option name="closed" value="true" />
      <created>1743069561939</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1743069561940</updated>
    </task>
    <task id="LOCAL-00010" summary="refactor(dataSource): 重构数据源连接测试逻辑&#10;&#10;- 修改 DataSourceDao 接口，简化 updateConnectionStatus 方法参数&#10;- 重构 DataSourceServiceImpl 中的 testDatabaseConnection 方法&#10;- 新增 DriverShim 类，用于动态加载数据库驱动&#10;- 更新 DruidConfig，移除 DriverShim内部类&#10;- 调整 DruidConnectionTester，用于测试数据源连接">
      <option name="closed" value="true" />
      <created>1743157553886</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1743157553886</updated>
    </task>
    <task id="LOCAL-00011" summary="feat(ui): 新增数据源实体类和分页查询接口实现">
      <option name="closed" value="true" />
      <created>1743587935564</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1743587935564</updated>
    </task>
    <task id="LOCAL-00012" summary="feat(auto-relay): 重构数据交换模块的数据库驱动加载方式- 新增 DbDriver 类封装数据库驱动信息&#10;- 修改 DatabaseUtil 类，使用 DbDriver 替代原始的驱动路径字符串&#10;- 更新 DruidConfig 类，支持使用 DbDriver 初始化数据源&#10;- 优化代码结构，提高可维护性和可扩展性">
      <option name="closed" value="true" />
      <created>1743825383220</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1743825383220</updated>
    </task>
    <task id="LOCAL-00013" summary="feat(auto-relay): 添加参数映射功能&#10;&#10;- 新增参数映射相关实体类、DTO、Mapper、Service等&#10;- 实现参数映射的创建、查询、保存、删除等功能&#10;- 添加数据源表列表和字段列表获取方法">
      <option name="closed" value="true" />
      <created>1743825567944</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1743825567944</updated>
    </task>
    <task id="LOCAL-00014" summary="refactor(admin): 移除参数映射相关代码&#10;&#10;- 删除了 InterfaceConfigService 和 InterfaceInvokeService 中与参数映射相关的代码&#10;- 移除了 ParamMapping、ParamMappingItem、ParamMappingCreateDTO等相关类&#10;- 删除了 ParamMappingMapper、ParamMappingItemMapper等相关 Mapper 接口和 XML 文件&#10;- 移除了 ParamMappingService 接口及其实现类">
      <option name="closed" value="true" />
      <created>1744109455124</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1744109455124</updated>
    </task>
    <task id="LOCAL-00015" summary="refactor(interface-config): 重构接口配置模块&#10;&#10;- 新增数据源 ID 和表名字段&#10;- 修改字典转换逻辑，增加系统编码参数&#10;- 优化参数映射和字典转换相关代码&#10;- 删除不必要的字段和方法&#10;-调整数据库表结构和 mapper 文件">
      <option name="closed" value="true" />
      <created>1744194430405</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1744194430405</updated>
    </task>
    <task id="LOCAL-00016" summary="feat(interfaceConfig): 增加 SQL 查询功能并优化表列表展示&#10;&#10;- 在 InterfaceConfig 模块中添加 executeSql 方法执行 SQL 查询&#10;- 修改 getTables 方法返回表信息列表，包括表名和注释&#10;- 更新相关 DTO 和 VO 类以支持 SQL 查询和表信息展示&#10;- 优化数据库连接和查询逻辑，提高代码复用性和可维护性">
      <option name="closed" value="true" />
      <created>1744281753944</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1744281753944</updated>
    </task>
    <task id="LOCAL-00017" summary="feat(autoRealy): 添加 SQL执行相关工具类和数据传输对象&#10;&#10;- 新增 SqlExecutorUtil工具类，用于执行 SQL 查询和非查询操作&#10;- 添加 SqlQueryDTO、SqlQueryResultVO 和 TableInfoDTO 等数据传输对象&#10;- 实现了对不同数据库类型（如 MySQL、Oracle、PostgreSQL、SQL Server）的行数限制处理&#10;- 提供了查询 SQL 和非查询 SQL 的执行方法，以及结果的封装和返回">
      <option name="closed" value="true" />
      <created>1744281796774</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1744281796774</updated>
    </task>
    <task id="LOCAL-00018" summary="feat(interface-config): 增加接口测试相关功能&#10;&#10;- 新增生成示例数据接口和测试接口完整流程接口&#10;- 实现示例数据生成和接口测试的业务逻辑&#10;- 增加加密和签名相关的配置项&#10;-优化参数映射和字典转换的处理">
      <option name="closed" value="true" />
      <created>1744628930922</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1744628930922</updated>
    </task>
    <task id="LOCAL-00019" summary="feat(interface): 添加接口测试功能并优化相关服务&#10;&#10;- 新增接口测试请求处理方法&#10;- 重构 WSDL 解析方法，支持返回详细解析信息&#10;- 优化 SQL 查询和接口调用相关服务- 添加 Webservice 操作的附加信息字段">
      <option name="closed" value="true" />
      <created>1744719865727</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1744719865727</updated>
    </task>
    <task id="LOCAL-00020" summary="refactor(ly-admin): 重构接口测试完整流程&#10;&#10;- 修改了接口测试完整流程的逻辑，优化了数据处理和模板解析&#10;- 新增 tableName 字段用于指定数据源表名&#10;- 改进了数据模板的处理方式，支持更复杂的模板结构&#10;- 优化了错误处理和日志记录">
      <option name="closed" value="true" />
      <created>1744975339313</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1744975339313</updated>
    </task>
    <task id="LOCAL-00021" summary="refactor(ly-admin):调整分批处理逻辑和默认批次大小&#10;&#10;- 反转分批处理启用条件，使代码逻辑更清晰&#10;- 将默认批次大小从 100调整为 10，以适应数据量较少的推送">
      <option name="closed" value="true" />
      <created>1753513792660</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753513792660</updated>
    </task>
    <task id="LOCAL-00022" summary="refactor(ly-admin):调整分批处理逻辑和默认批次大小&#10;&#10;- 反转分批处理启用条件，使代码逻辑更清晰&#10;- 将默认批次大小从 100调整为 10，以适应数据量较少的推送">
      <option name="closed" value="true" />
      <created>1753522653541</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753522653541</updated>
    </task>
    <task id="LOCAL-00023" summary="refactor(ly-admin): 优化接口任务日志记录&#10;&#10;- 在 InterfaceTaskService 中添加日志记录的空行，提高代码可读性&#10;- 此修改虽小，但有助于提升整体代码结构和维护性">
      <option name="closed" value="true" />
      <created>1753525603153</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753525603153</updated>
    </task>
    <task id="LOCAL-00024" summary="refactor(autoRealy): 优化推送批次处理逻辑&#10;&#10;- 修改了获取上次处理完的ID的逻辑，使用进行中的批次替代最新完成的批次&#10;- 优化了全量推送和增量推送模式下的起始ID处理逻辑&#10;-调整了创建新批次的流程，增加了日志记录">
      <option name="closed" value="true" />
      <created>1753631877248</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753631877248</updated>
    </task>
    <task id="LOCAL-00025" summary="feat(autoRealy): 增加目标数据 ID 字段支持- 在 PushConfigMapper.xml 中添加 target_data_id_field 字段&#10;- 修改 BatchProcessingService、InterfaceTaskService、PushRecordService 和 PushResponseParseService&#10;  以支持新的 targetDataIdField 参数&#10;- 优化 getCurrentBatchMaxId 方法，使用 targetDataIdField 替代固定的 ID 字段">
      <option name="closed" value="true" />
      <created>1753769555978</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753769555978</updated>
    </task>
    <task id="LOCAL-00026" summary="fix(autoRealy): 优化数据推送结果处理逻辑&#10;&#10;- 修复接口调用失败时的数据处理问题&#10;- 优化成功和失败数据的区分与处理&#10;- 新增从Redis中移除失败数据预标记的功能&#10;- 改进错误信息的记录方式，确保每条数据的错误信息都能正确保存- 重构数据推送结果解析逻辑，提高处理效率和准确性">
      <option name="closed" value="true" />
      <created>1754620868304</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1754620868304</updated>
    </task>
    <task id="LOCAL-00027" summary="fix(autoRealy): 修复数据推送逻辑和更新最大 ID 的问题&#10;&#10;- 修复 getCurrentBatchMaxId 方法使用错误的字段名&#10;- 优化 BatchProcessingResult 类，添加 Getters 和 Setters&#10;-修复 InterfaceTaskService 中的数据处理和错误日志&#10;- 优化 PushRecordService 中的失败记录和预标记逻辑&#10;- 修复 RedisService 中的 hasKey 方法返回值类型">
      <option name="closed" value="true" />
      <created>1754666702380</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1754666702380</updated>
    </task>
    <task id="LOCAL-00028" summary="ci: 更新数据库配置和环境设置&#10;&#10;- 修改开发环境和生产环境的数据库连接信息&#10;- 更新 Redis 密码配置&#10;-调整文件上传路径&#10;- 修改 Maven 配置文件，设置生产环境为默认激活配置">
      <option name="closed" value="true" />
      <created>1754896243732</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1754896243732</updated>
    </task>
    <task id="LOCAL-00029" summary="refactor: 优化数据库配置文件格式&#10;&#10;- 移除了多余的空行和密码字段后的多余空格&#10;- 注释掉的数据库连接信息保持不变">
      <option name="closed" value="true" />
      <created>1754896930869</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1754896930869</updated>
    </task>
    <option name="localTasksCounter" value="30" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="SHOW_DIRTY_RECURSIVELY" value="true" />
    <MESSAGE value="feat(auto-relay): 添加数据源管理功能&#10;&#10;- 新增数据源和数据库驱动相关表结构&#10;- 实现数据源管理的 CRUD接口和服务&#10;- 添加数据源连接测试和设置默认数据源功能&#10;- 集成数据追踪服务，记录数据源变更" />
    <MESSAGE value="feat(dataSource): 动态连接数据库并获取表信息&#10;&#10;- 新增接口和实现类，支持动态连接数据库并获取表名列表&#10;- 优化数据库驱动上传逻辑，确保驱动文件正确保存&#10;- 添加必要的配置项，支持文件上传&#10;- 更新.gitignore文件，忽略编译后的目标文件" />
    <MESSAGE value="feat(autoRealy): 添加数据库连接和查询工具类&#10;&#10;- 新增 DatabaseTestUtil 和 DatabaseUtil 类，用于获取表名和列名- 添加 DriverShim 类，用于包装数据库驱动&#10;- 新增 DruidConfig 类，配置 Druid 数据源- 添加 DruidConnectionTester 类，用于测试数据库连接" />
    <MESSAGE value="refactor(autoRealy): 重构数据源管理功能&#10;&#10;- 将 DataSource 类重命名为 AutoDataSource&#10;- 重构 DatabaseUtil 类，支持多种数据库类型&#10;- 更新数据源管理相关的控制器、服务、DAO 和 XML 文件&#10;- 将 DruidConfig 类移动到 util 包，并改为动态初始化数据源" />
    <MESSAGE value="refactor(dataSource): 重构数据源连接测试逻辑&#10;&#10;- 修改 DataSourceDao 接口，简化 updateConnectionStatus 方法参数&#10;- 重构 DataSourceServiceImpl 中的 testDatabaseConnection 方法&#10;- 新增 DriverShim 类，用于动态加载数据库驱动&#10;- 更新 DruidConfig，移除 DriverShim内部类&#10;- 调整 DruidConnectionTester，用于测试数据源连接" />
    <MESSAGE value="feat(ui): 新增数据源实体类和分页查询接口实现" />
    <MESSAGE value="feat(auto-relay): 重构数据交换模块的数据库驱动加载方式- 新增 DbDriver 类封装数据库驱动信息&#10;- 修改 DatabaseUtil 类，使用 DbDriver 替代原始的驱动路径字符串&#10;- 更新 DruidConfig 类，支持使用 DbDriver 初始化数据源&#10;- 优化代码结构，提高可维护性和可扩展性" />
    <MESSAGE value="feat(auto-relay): 添加参数映射功能&#10;&#10;- 新增参数映射相关实体类、DTO、Mapper、Service等&#10;- 实现参数映射的创建、查询、保存、删除等功能&#10;- 添加数据源表列表和字段列表获取方法" />
    <MESSAGE value="refactor(admin): 移除参数映射相关代码&#10;&#10;- 删除了 InterfaceConfigService 和 InterfaceInvokeService 中与参数映射相关的代码&#10;- 移除了 ParamMapping、ParamMappingItem、ParamMappingCreateDTO等相关类&#10;- 删除了 ParamMappingMapper、ParamMappingItemMapper等相关 Mapper 接口和 XML 文件&#10;- 移除了 ParamMappingService 接口及其实现类" />
    <MESSAGE value="refactor(interface-config): 重构接口配置模块&#10;&#10;- 新增数据源 ID 和表名字段&#10;- 修改字典转换逻辑，增加系统编码参数&#10;- 优化参数映射和字典转换相关代码&#10;- 删除不必要的字段和方法&#10;-调整数据库表结构和 mapper 文件" />
    <MESSAGE value="feat(interfaceConfig): 增加 SQL 查询功能并优化表列表展示&#10;&#10;- 在 InterfaceConfig 模块中添加 executeSql 方法执行 SQL 查询&#10;- 修改 getTables 方法返回表信息列表，包括表名和注释&#10;- 更新相关 DTO 和 VO 类以支持 SQL 查询和表信息展示&#10;- 优化数据库连接和查询逻辑，提高代码复用性和可维护性" />
    <MESSAGE value="feat(autoRealy): 添加 SQL执行相关工具类和数据传输对象&#10;&#10;- 新增 SqlExecutorUtil工具类，用于执行 SQL 查询和非查询操作&#10;- 添加 SqlQueryDTO、SqlQueryResultVO 和 TableInfoDTO 等数据传输对象&#10;- 实现了对不同数据库类型（如 MySQL、Oracle、PostgreSQL、SQL Server）的行数限制处理&#10;- 提供了查询 SQL 和非查询 SQL 的执行方法，以及结果的封装和返回" />
    <MESSAGE value="feat(interface-config): 增加接口测试相关功能&#10;&#10;- 新增生成示例数据接口和测试接口完整流程接口&#10;- 实现示例数据生成和接口测试的业务逻辑&#10;- 增加加密和签名相关的配置项&#10;-优化参数映射和字典转换的处理" />
    <MESSAGE value="feat(interface): 添加接口测试功能并优化相关服务&#10;&#10;- 新增接口测试请求处理方法&#10;- 重构 WSDL 解析方法，支持返回详细解析信息&#10;- 优化 SQL 查询和接口调用相关服务- 添加 Webservice 操作的附加信息字段" />
    <MESSAGE value="refactor(ly-admin): 重构接口测试完整流程&#10;&#10;- 修改了接口测试完整流程的逻辑，优化了数据处理和模板解析&#10;- 新增 tableName 字段用于指定数据源表名&#10;- 改进了数据模板的处理方式，支持更复杂的模板结构&#10;- 优化了错误处理和日志记录" />
    <MESSAGE value="refactor(ly-admin):调整分批处理逻辑和默认批次大小&#10;&#10;- 反转分批处理启用条件，使代码逻辑更清晰&#10;- 将默认批次大小从 100调整为 10，以适应数据量较少的推送" />
    <MESSAGE value="refactor(ly-admin): 优化" />
    <MESSAGE value="refactor(ly-admin): 优化接口任务日志记录&#10;&#10;- 在 InterfaceTaskService 中添加日志记录的空行，提高代码可读性&#10;- 此修改虽小，但有助于提升整体代码结构和维护性" />
    <MESSAGE value="refactor(autoRealy): 优化推送批次处理逻辑&#10;&#10;- 修改了获取上次处理完的ID的逻辑，使用进行中的批次替代最新完成的批次&#10;- 优化了全量推送和增量推送模式下的起始ID处理逻辑&#10;-调整了创建新批次的流程，增加了日志记录" />
    <MESSAGE value="feat(autoRealy): 增加目标数据 ID 字段支持- 在 PushConfigMapper.xml 中添加 target_data_id_field 字段&#10;- 修改 BatchProcessingService、InterfaceTaskService、PushRecordService 和 PushResponseParseService&#10;  以支持新的 targetDataIdField 参数&#10;- 优化 getCurrentBatchMaxId 方法，使用 targetDataIdField 替代固定的 ID 字段" />
    <MESSAGE value="fix(autoRealy): 优化数据推送结果处理逻辑&#10;&#10;- 修复接口调用失败时的数据处理问题&#10;- 优化成功和失败数据的区分与处理&#10;- 新增从Redis中移除失败数据预标记的功能&#10;- 改进错误信息的记录方式，确保每条数据的错误信息都能正确保存- 重构数据推送结果解析逻辑，提高处理效率和准确性" />
    <MESSAGE value="refactor(ly-admin): 优化自动推送模块代码结构和逻辑- 在 BatchProcessingService 中使用 Lombok 注解简化代码&#10;-修复 InterfaceTaskService 中的数据处理逻辑，提高代码可读性&#10;- 优化 PushRecordService 中的失败结果记录方法&#10;- 改进 RedisService 中的键存在性检查方法，确保返回值类型正确" />
    <MESSAGE value="fix(autoRealy): 修复数据推送逻辑和更新最大 ID 的问题&#10;&#10;- 修复 getCurrentBatchMaxId 方法使用错误的字段名&#10;- 优化 BatchProcessingResult 类，添加 Getters 和 Setters&#10;-修复 InterfaceTaskService 中的数据处理和错误日志&#10;- 优化 PushRecordService 中的失败记录和预标记逻辑&#10;- 修复 RedisService 中的 hasKey 方法返回值类型" />
    <MESSAGE value="ci: 更新数据库配置和环境设置&#10;&#10;- 修改开发环境和生产环境的数据库连接信息&#10;- 更新 Redis 密码配置&#10;-调整文件上传路径&#10;- 修改 Maven 配置文件，设置生产环境为默认激活配置" />
    <MESSAGE value="refactor: 优化数据库配置文件格式&#10;&#10;- 移除了多余的空行和密码字段后的多余空格&#10;- 注释掉的数据库连接信息保持不变" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor: 优化数据库配置文件格式&#10;&#10;- 移除了多余的空行和密码字段后的多余空格&#10;- 注释掉的数据库连接信息保持不变" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceConfig/service/InterfaceInvokeService.java</url>
          <line>1028</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceTask/service/InterfaceTaskService.java</url>
          <line>231</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceConfig/controller/InterfaceConfigController.java</url>
          <line>213</line>
          <option name="timeStamp" value="87" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/util/DatabaseUtil.java</url>
          <line>149</line>
          <option name="timeStamp" value="94" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ly-admin/src/main/java/net/lingyue/ly/admin/autoRealy/interfaceConfig/service/InterfaceConfigService.java</url>
          <line>731</line>
          <option name="timeStamp" value="103" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>