<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.base.module.support.dict.dao.DictValueDao">

    <update id="updateDeletedFlagByIdList">
        update t_dict_value set deleted_flag = #{deletedFlag} where dict_value_id in
        <foreach collection="dictValueIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <select id="query" resultType="net.lingyue.ly.base.module.support.dict.domain.vo.DictValueVO">
        SELECT * FROM t_dict_value
        <where>
            <if test="query.dictKeyId != null">
                AND dict_key_id = #{query.dictKeyId}
            </if>
            <if test="query.searchWord != null and query.searchWord !=''">
                AND (INSTR(value_code,#{query.searchWord}) or INSTR(value_name,#{query.searchWord}))
            </if>
            <if test="query.deletedFlag != null">
                AND deleted_flag = #{query.deletedFlag}
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY sort,dict_value_id DESC
        </if>
    </select>

    <select id="selectByCode"
            resultType="net.lingyue.ly.base.module.support.dict.domain.entity.DictValueEntity">
        select * from t_dict_value where value_code = #{valueCode} and deleted_flag = #{deletedFlag}
    </select>

    <select id="selectByDeletedFlag"
            resultType="net.lingyue.ly.base.module.support.dict.domain.entity.DictValueEntity">
        select * from t_dict_value where deleted_flag = #{deletedFlag} order by sort;
    </select>
    <select id="selectByDeletedFlagAndKeyId"
            resultType="net.lingyue.ly.base.module.support.dict.domain.entity.DictValueEntity">
        select * from t_dict_value where dict_key_id = #{dictKeyId} and deleted_flag = #{deletedFlag}
    </select>

</mapper>
