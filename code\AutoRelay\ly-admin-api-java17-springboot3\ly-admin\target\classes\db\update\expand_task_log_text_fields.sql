-- 扩展接口任务日志表的文本字段长度
-- 解决 "Data too long for column 'request_body'" 错误

-- 修改 request_body 字段为 LONGTEXT (支持4GB数据)
ALTER TABLE `t_interface_task_log` 
MODIFY COLUMN `request_body` LONGTEXT DEFAULT NULL COMMENT '请求体';

-- 修改 response_body 字段为 LONGTEXT (支持4GB数据)
ALTER TABLE `t_interface_task_log` 
MODIFY COLUMN `response_body` LONGTEXT DEFAULT NULL COMMENT '响应体';

-- 修改 execute_result 字段为 LONGTEXT (支持4GB数据)
ALTER TABLE `t_interface_task_log` 
MODIFY COLUMN `execute_result` LONGTEXT DEFAULT NULL COMMENT '上报数据详情';

-- 添加索引以提升查询性能 (LONGTEXT字段需要指定前缀长度)
-- 如果索引已存在则跳过
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 't_interface_task_log' 
         AND INDEX_NAME = 'idx_request_body_prefix') = 0,
        'CREATE INDEX idx_request_body_prefix ON t_interface_task_log (request_body(255))',
        'SELECT "Index idx_request_body_prefix already exists"'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;