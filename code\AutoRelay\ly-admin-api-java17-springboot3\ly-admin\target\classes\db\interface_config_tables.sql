-- 接口调用配置相关表初始化脚本
-- 创建时间：2024-07-05

-- 创建接口配置表
CREATE TABLE IF NOT EXISTS `t_interface_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `system_name` varchar(100) NOT NULL COMMENT '系统名称',
  `interface_name` varchar(100) NOT NULL COMMENT '接口名称',
  `interface_type` varchar(20) NOT NULL COMMENT '接口类型：REST、WEBSERVICE、CUSTOM',
  `request_url` varchar(500) NOT NULL COMMENT '请求URL',
  `request_method` varchar(20) NOT NULL COMMENT '请求方法：GET、POST、PUT、DELETE、PATCH',
  `web_service_operation` varchar(100) DEFAULT NULL COMMENT 'WebService操作名称',
  `param_mapping_id` bigint(20) DEFAULT NULL COMMENT '参数映射ID',
  `request_body_type` varchar(20) DEFAULT 'JSON' COMMENT '请求体类型：JSON、FORM、XML、NONE',
  `request_body_template` text DEFAULT NULL COMMENT '请求体模板',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0停用 1启用',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_system_code` (`system_code`) USING BTREE,
  KEY `idx_param_mapping_id` (`param_mapping_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口配置表';

-- 创建接口请求头表
CREATE TABLE IF NOT EXISTS `t_interface_header` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `interface_id` bigint(20) NOT NULL COMMENT '接口配置ID',
  `name` varchar(100) NOT NULL COMMENT '请求头名称',
  `value` varchar(500) NOT NULL COMMENT '请求头值',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_interface_id` (`interface_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口请求头表';

-- 创建接口参数映射表
CREATE TABLE IF NOT EXISTS `t_interface_param_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `interface_id` bigint(20) NOT NULL COMMENT '接口配置ID',
  `param_name` varchar(100) NOT NULL COMMENT '源参数名',
  `target_param_name` varchar(100) NOT NULL COMMENT '目标参数名',
  `dict_type_id` bigint(20) DEFAULT NULL COMMENT '字典类型ID',
  `required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填 0否 1是',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_interface_id` (`interface_id`) USING BTREE,
  KEY `idx_dict_type_id` (`dict_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口参数映射表';