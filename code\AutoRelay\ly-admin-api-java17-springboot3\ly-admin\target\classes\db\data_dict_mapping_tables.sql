-- 数据字典映射相关表初始化脚本
-- 创建时间：2024-12-28
-- 用途：实现源数据到目标数据的映射转换

-- 创建第三方系统管理表
CREATE TABLE IF NOT EXISTS `t_third_party_system` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `system_name` varchar(100) NOT NULL COMMENT '系统名称',
  `system_desc` varchar(500) DEFAULT NULL COMMENT '系统描述',
  `base_url` varchar(200) DEFAULT NULL COMMENT '系统基础URL',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0停用 1启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_code` (`system_code`,`deleted_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方系统表';

-- 插入示例系统数据
INSERT INTO `t_third_party_system` (`system_code`, `system_name`, `system_desc`, `contact_person`, `contact_phone`, `status`, `remark`, `create_time`, `update_time`) VALUES
('THIRD_PARTY_SYSTEM', '第三方系统', '用于测试的第三方系统', '张三', '13800138000', 1, '测试系统', NOW(), NOW());

-- 创建数据字典映射表
CREATE TABLE IF NOT EXISTS `t_data_dict_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '第三方系统编码',
  `dict_type_code` varchar(50) NOT NULL COMMENT '字典类型编码',
  `dict_type_name` varchar(100) NOT NULL COMMENT '字典类型名称',
  `source_value` varchar(100) NOT NULL COMMENT '源数据值',
  `source_name` varchar(100) DEFAULT NULL COMMENT '源数据名称',
  `target_value` varchar(100) NOT NULL COMMENT '目标数据值', 
  `target_name` varchar(100) DEFAULT NULL COMMENT '目标数据名称',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_dict_source` (`system_code`,`dict_type_code`,`source_value`,`deleted_flag`) USING BTREE,
  KEY `idx_system_code` (`system_code`) USING BTREE,
  KEY `idx_dict_type_code` (`dict_type_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典映射表';

-- 创建数据字典类型表（用于管理字典类型）
CREATE TABLE IF NOT EXISTS `t_data_dict_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `system_code` varchar(50) NOT NULL COMMENT '第三方系统编码',
  `dict_type_code` varchar(50) NOT NULL COMMENT '字典类型编码',
  `dict_type_name` varchar(100) NOT NULL COMMENT '字典类型名称',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0停用 1启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_dict_type` (`system_code`,`dict_type_code`,`deleted_flag`) USING BTREE,
  KEY `idx_system_code` (`system_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典类型表';

-- 插入示例数据
INSERT INTO `t_data_dict_type` (`system_code`, `dict_type_code`, `dict_type_name`, `description`, `status`, `sort_order`, `remark`, `create_time`, `update_time`) VALUES
('THIRD_PARTY_SYSTEM', 'CERTIFICATE_TYPE', '证件类型', '各种证件类型的字典映射', 1, 1, '用于证件类型转换', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'GENDER', '性别', '性别字典映射', 1, 2, '用于性别转换', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '学历字典映射', 1, 3, '用于学历转换', NOW(), NOW());

-- 插入示例映射数据
INSERT INTO `t_data_dict_mapping` (`system_code`, `dict_type_code`, `dict_type_name`, `source_value`, `source_name`, `target_value`, `target_name`, `sort`, `remark`, `create_time`, `update_time`) VALUES
('THIRD_PARTY_SYSTEM', 'CERTIFICATE_TYPE', '证件类型', '1', '身份证', '01', '居民身份证', 1, '身份证映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'CERTIFICATE_TYPE', '证件类型', '2', '护照', '02', '中华人民共和国护照', 2, '护照映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'CERTIFICATE_TYPE', '证件类型', '3', '军官证', '03', '军官证', 3, '军官证映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'CERTIFICATE_TYPE', '证件类型', '4', '驾驶证', '04', '机动车驾驶证', 4, '驾驶证映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'GENDER', '性别', '1', '男', '0', '男性', 1, '男性映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'GENDER', '性别', '2', '女', '1', '女性', 2, '女性映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '1', '小学', '01', '小学', 1, '小学映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '2', '初中', '02', '初中', 2, '初中映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '3', '高中', '03', '高中', 3, '高中映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '4', '大专', '04', '大专', 4, '大专映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '5', '本科', '05', '本科', 5, '本科映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '6', '硕士', '06', '硕士研究生', 6, '硕士映射', NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '7', '博士', '07', '博士研究生', 7, '博士映射', NOW(), NOW());