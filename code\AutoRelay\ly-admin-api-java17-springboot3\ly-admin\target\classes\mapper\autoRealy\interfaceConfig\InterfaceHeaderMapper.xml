<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.autoRealy.interfaceConfig.dao.InterfaceHeaderDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.InterfaceHeaderEntity">
        <id column="id" property="id" />
        <result column="interface_id" property="interfaceId" />
        <result column="name" property="name" />
        <result column="value" property="value" />
        <result column="description" property="description" />
        <result column="deleted_flag" property="deletedFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, interface_id, name, value, description, deleted_flag, create_time, update_time
    </sql>
    
    <!-- 根据接口ID查询请求头列表 -->
    <select id="selectByInterfaceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_interface_header
        WHERE interface_id = #{interfaceId}
        AND deleted_flag = 0
    </select>
    
    <!-- 根据接口ID删除请求头 -->
    <delete id="deleteByInterfaceId">
        DELETE FROM t_interface_header
        WHERE interface_id = #{interfaceId}
    </delete>
    
    <!-- 批量插入请求头 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_interface_header
        (interface_id, name, value, description, deleted_flag, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.interfaceId}, #{item.name}, #{item.value}, #{item.description}, 
            0, NOW(), NOW())
        </foreach>
    </insert>
    
    <!-- 根据ID查询请求头 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_interface_header
        WHERE id = #{id}
        AND deleted_flag = 0
    </select>
    
    <!-- 批量删除请求头（逻辑删除） -->
    <delete id="batchDelete">
        DELETE FROM t_interface_header
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_flag = 0
    </delete>
    
</mapper>