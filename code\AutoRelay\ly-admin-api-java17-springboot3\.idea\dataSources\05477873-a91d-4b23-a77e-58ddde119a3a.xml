<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="阿里云">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.52">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||aliyun_root|127.0.0.1|ALTER|G
|root||aurora||ALTER|G
|root||lingyuelabs||ALTER|G
|root||aliyun_root|127.0.0.1|ALTER ROUTINE|G
|root||aurora||ALTER ROUTINE|G
|root||lingyuelabs||ALTER ROUTINE|G
|root||aliyun_root|127.0.0.1|APPLICATION_PASSWORD_ADMIN|G
|root||aurora||APPLICATION_PASSWORD_ADMIN|G
|root||aliyun_root|127.0.0.1|AUDIT_ABORT_EXEMPT|G
|root||aurora||AUDIT_ABORT_EXEMPT|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||aliyun_root|127.0.0.1|AUDIT_ADMIN|G
|root||aurora||AUDIT_ADMIN|G
|root||aliyun_root|127.0.0.1|AUTHENTICATION_POLICY_ADMIN|G
|root||aurora||AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||aliyun_root|127.0.0.1|BACKUP_ADMIN|G
|root||aurora||BACKUP_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||aliyun_root|127.0.0.1|BINLOG_ADMIN|G
|root||aurora||BINLOG_ADMIN|G
|root||aliyun_root|127.0.0.1|BINLOG_ENCRYPTION_ADMIN|G
|root||aurora||BINLOG_ENCRYPTION_ADMIN|G
|root||aliyun_root|127.0.0.1|CLONE_ADMIN|G
|root||aurora||CLONE_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||aliyun_root|127.0.0.1|CONNECTION_ADMIN|G
|root||aurora||CONNECTION_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||aliyun_root|127.0.0.1|CREATE|G
|root||aurora||CREATE|G
|root||lingyuelabs||CREATE|G
|root||aliyun_root|127.0.0.1|CREATE ROLE|G
|root||aurora||CREATE ROLE|G
|root||aliyun_root|127.0.0.1|CREATE ROUTINE|G
|root||aurora||CREATE ROUTINE|G
|root||lingyuelabs||CREATE ROUTINE|G
|root||aliyun_root|127.0.0.1|CREATE TABLESPACE|G
|root||aurora||CREATE TABLESPACE|G
|root||aliyun_root|127.0.0.1|CREATE TEMPORARY TABLES|G
|root||aurora||CREATE TEMPORARY TABLES|G
|root||lingyuelabs||CREATE TEMPORARY TABLES|G
|root||aliyun_root|127.0.0.1|CREATE USER|G
|root||aurora||CREATE USER|G
|root||lingyuelabs||CREATE USER|G
|root||aliyun_root|127.0.0.1|CREATE VIEW|G
|root||aurora||CREATE VIEW|G
|root||lingyuelabs||CREATE VIEW|G
|root||aliyun_root|127.0.0.1|DELETE|G
|root||aurora||DELETE|G
|root||lingyuelabs||DELETE|G
|root||aliyun_root|127.0.0.1|DROP|G
|root||aurora||DROP|G
|root||lingyuelabs||DROP|G
|root||aliyun_root|127.0.0.1|DROP ROLE|G
|root||aurora||DROP ROLE|G
|root||aliyun_root|127.0.0.1|ENCRYPTION_KEY_ADMIN|G
|root||aurora||ENCRYPTION_KEY_ADMIN|G
|root||aliyun_root|127.0.0.1|EVENT|G
|root||aurora||EVENT|G
|root||lingyuelabs||EVENT|G
|root||aliyun_root|127.0.0.1|EXECUTE|G
|root||aurora||EXECUTE|G
|root||lingyuelabs||EXECUTE|G
|root||aliyun_root|127.0.0.1|FILE|G
|root||aurora||FILE|G
|root||aliyun_root|127.0.0.1|FIREWALL_EXEMPT|G
|root||aurora||FIREWALL_EXEMPT|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||aliyun_root|127.0.0.1|FLUSH_OPTIMIZER_COSTS|G
|root||aurora||FLUSH_OPTIMIZER_COSTS|G
|root||aliyun_root|127.0.0.1|FLUSH_STATUS|G
|root||aurora||FLUSH_STATUS|G
|root||aliyun_root|127.0.0.1|FLUSH_TABLES|G
|root||aurora||FLUSH_TABLES|G
|root||aliyun_root|127.0.0.1|FLUSH_USER_RESOURCES|G
|root||aurora||FLUSH_USER_RESOURCES|G
|root||aliyun_root|127.0.0.1|GROUP_REPLICATION_ADMIN|G
|root||aurora||GROUP_REPLICATION_ADMIN|G
|root||aliyun_root|127.0.0.1|GROUP_REPLICATION_STREAM|G
|root||aurora||GROUP_REPLICATION_STREAM|G
|root||aliyun_root|127.0.0.1|INDEX|G
|root||aurora||INDEX|G
|root||lingyuelabs||INDEX|G
|root||aliyun_root|127.0.0.1|INNODB_REDO_LOG_ARCHIVE|G
|root||aurora||INNODB_REDO_LOG_ARCHIVE|G
|root||aliyun_root|127.0.0.1|INNODB_REDO_LOG_ENABLE|G
|root||aurora||INNODB_REDO_LOG_ENABLE|G
|root||aliyun_root|127.0.0.1|INSERT|G
|root||aurora||INSERT|G
|root||lingyuelabs||INSERT|G
|root||aliyun_root|127.0.0.1|LOCK TABLES|G
|root||aurora||LOCK TABLES|G
|root||lingyuelabs||LOCK TABLES|G
|root||aliyun_root|127.0.0.1|PASSWORDLESS_USER_ADMIN|G
|root||aurora||PASSWORDLESS_USER_ADMIN|G
|root||aliyun_root|127.0.0.1|PERSIST_RO_VARIABLES_ADMIN|G
|root||aurora||PERSIST_RO_VARIABLES_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||aliyun_root|127.0.0.1|PROCESS|G
|root||aurora||PROCESS|G
|root||lingyuelabs||PROCESS|G
|root||portalcontent||PROCESS|G
|root||aliyun_root|127.0.0.1|REFERENCES|G
|root||aurora||REFERENCES|G
|root||lingyuelabs||REFERENCES|G
|root||aliyun_root|127.0.0.1|RELOAD|G
|root||aurora||RELOAD|G
|root||lingyuelabs||RELOAD|G
|root||aliyun_root|127.0.0.1|REPLICATION CLIENT|G
|root||aurora||REPLICATION CLIENT|G
|root||lingyuelabs||REPLICATION CLIENT|G
|root||portalcontent||REPLICATION CLIENT|G
|root||aliyun_root|127.0.0.1|REPLICATION SLAVE|G
|root||aurora||REPLICATION SLAVE|G
|root||lingyuelabs||REPLICATION SLAVE|G
|root||portalcontent||REPLICATION SLAVE|G
|root||aliyun_root|127.0.0.1|REPLICATION_APPLIER|G
|root||aurora||REPLICATION_APPLIER|G
|root||aliyun_root|127.0.0.1|REPLICATION_SLAVE_ADMIN|G
|root||aurora||REPLICATION_SLAVE_ADMIN|G
|root||aliyun_root|127.0.0.1|RESOURCE_GROUP_ADMIN|G
|root||aurora||RESOURCE_GROUP_ADMIN|G
|root||aliyun_root|127.0.0.1|RESOURCE_GROUP_USER|G
|root||aurora||RESOURCE_GROUP_USER|G
|root||aliyun_root|127.0.0.1|ROLE_ADMIN|G
|root||aurora||ROLE_ADMIN|G
|root||aliyun_root|127.0.0.1|SELECT|G
|root||aurora||SELECT|G
|root||lingyuelabs||SELECT|G
|root||mysql.infoschema|localhost|SELECT|G
|root||aliyun_root|127.0.0.1|SENSITIVE_VARIABLES_OBSERVER|G
|root||aurora||SENSITIVE_VARIABLES_OBSERVER|G
|root||aliyun_root|127.0.0.1|SERVICE_CONNECTION_ADMIN|G
|root||aurora||SERVICE_CONNECTION_ADMIN|G
|root||aliyun_root|127.0.0.1|SESSION_VARIABLES_ADMIN|G
|root||aurora||SESSION_VARIABLES_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||aliyun_root|127.0.0.1|SET_USER_ID|G
|root||aurora||SET_USER_ID|G
|root||aliyun_root|127.0.0.1|SHOW DATABASES|G
|root||aurora||SHOW DATABASES|G
|root||lingyuelabs||SHOW DATABASES|G
|root||aliyun_root|127.0.0.1|SHOW VIEW|G
|root||aurora||SHOW VIEW|G
|root||lingyuelabs||SHOW VIEW|G
|root||aliyun_root|127.0.0.1|SHOW_ROUTINE|G
|root||aurora||SHOW_ROUTINE|G
|root||aliyun_root|127.0.0.1|SHUTDOWN|G
|root||aurora||SHUTDOWN|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||aliyun_root|127.0.0.1|SUPER|G
|root||aurora||SUPER|G
|root||mysql.session|localhost|SUPER|G
|root||aliyun_root|127.0.0.1|SYSTEM_USER|G
|root||aurora||SYSTEM_USER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||aliyun_root|127.0.0.1|SYSTEM_VARIABLES_ADMIN|G
|root||aurora||SYSTEM_VARIABLES_ADMIN|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||aliyun_root|127.0.0.1|TABLE_ENCRYPTION_ADMIN|G
|root||aurora||TABLE_ENCRYPTION_ADMIN|G
|root||aliyun_root|127.0.0.1|TELEMETRY_LOG_ADMIN|G
|root||aurora||TELEMETRY_LOG_ADMIN|G
|root||aliyun_root|127.0.0.1|TRIGGER|G
|root||aurora||TRIGGER|G
|root||lingyuelabs||TRIGGER|G
|root||aliyun_root|127.0.0.1|UPDATE|G
|root||aurora||UPDATE|G
|root||lingyuelabs||UPDATE|G
|root||aliyun_root|127.0.0.1|XA_RECOVER_ADMIN|G
|root||aurora||XA_RECOVER_ADMIN|G
|root||portalcontent||XA_RECOVER_ADMIN|G
|root||aliyun_root|127.0.0.1|grant option|G
|root||aurora||grant option|G
|root||lingyuelabs||grant option|G
mysql|schema||aurora||ALTER|G
mysql|schema||aurora||ALTER ROUTINE|G
mysql|schema||aurora||CREATE|G
mysql|schema||aurora||CREATE ROUTINE|G
mysql|schema||aurora||CREATE TEMPORARY TABLES|G
mysql|schema||aurora||CREATE VIEW|G
mysql|schema||aurora||DELETE|G
mysql|schema||aurora||DROP|G
mysql|schema||aurora||EVENT|G
mysql|schema||aurora||EXECUTE|G
mysql|schema||aurora||INDEX|G
mysql|schema||aurora||INSERT|G
mysql|schema||aurora||LOCK TABLES|G
mysql|schema||aurora||REFERENCES|G
mysql|schema||aurora||SELECT|G
mysql|schema||aurora||SHOW VIEW|G
mysql|schema||aurora||TRIGGER|G
mysql|schema||aurora||UPDATE|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G
test|schema||aurora||ALTER|G
test|schema||aurora||ALTER ROUTINE|G
test|schema||aurora||CREATE|G
test|schema||aurora||CREATE ROUTINE|G
test|schema||aurora||CREATE TEMPORARY TABLES|G
test|schema||aurora||CREATE VIEW|G
test|schema||aurora||DELETE|G
test|schema||aurora||DROP|G
test|schema||aurora||EVENT|G
test|schema||aurora||EXECUTE|G
test|schema||aurora||INDEX|G
test|schema||aurora||INSERT|G
test|schema||aurora||LOCK TABLES|G
test|schema||aurora||REFERENCES|G
test|schema||aurora||SELECT|G
test|schema||aurora||SHOW VIEW|G
test|schema||aurora||TRIGGER|G
test|schema||aurora||UPDATE|G
test\\_one|schema||portalcontent||ALTER|G
test\\_one|schema||portalcontent||ALTER ROUTINE|G
test\\_one|schema||portalcontent||CREATE|G
test\\_one|schema||portalcontent||CREATE ROUTINE|G
test\\_one|schema||portalcontent||CREATE TEMPORARY TABLES|G
test\\_one|schema||portalcontent||CREATE VIEW|G
test\\_one|schema||portalcontent||DELETE|G
test\\_one|schema||portalcontent||DROP|G
test\\_one|schema||portalcontent||EVENT|G
test\\_one|schema||portalcontent||EXECUTE|G
test\\_one|schema||portalcontent||INDEX|G
test\\_one|schema||portalcontent||INSERT|G
test\\_one|schema||portalcontent||LOCK TABLES|G
test\\_one|schema||portalcontent||REFERENCES|G
test\\_one|schema||portalcontent||SELECT|G
test\\_one|schema||portalcontent||SHOW VIEW|G
test\\_one|schema||portalcontent||TRIGGER|G
test\\_one|schema||portalcontent||UPDATE|G</Grants>
      <ServerVersion>8.0.36</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="__recycle_bin__">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="education_java">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="fee-reduction">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="hci">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="hn_healthy">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="lingyue-admin">
      <LastIntrospectionLocalTimestamp>2025-09-05.03:03:50</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="295" parent="1" name="mysql">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="296" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="297" parent="1" name="portal-content">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="298" parent="1" name="project_management">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="299" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="300" parent="1" name="test_one">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="301" parent="1" name="aliyun_root">
      <Host>127.0.0.1</Host>
    </user>
    <user id="302" parent="1" name="aurora"/>
    <user id="303" parent="1" name="lingyuelabs"/>
    <user id="304" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="305" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="306" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="307" parent="1" name="portalcontent"/>
    <table id="308" parent="294" name="t_auto_relay_data_source">
      <Comment>数据源表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="294" name="t_auto_relay_db_driver">
      <Comment>数据库驱动表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="294" name="t_category">
      <Comment>分类表，主要用于商品分类</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="311" parent="294" name="t_change_log">
      <Comment>系统更新日志</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="312" parent="294" name="t_code_generator_config">
      <Comment>代码生成器的每个表的配置</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="313" parent="294" name="t_config">
      <Comment>系统配置</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="314" parent="294" name="t_data_dict_mapping">
      <Comment>数据字典映射表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="294" name="t_data_dict_type">
      <Comment>数据字典类型表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="316" parent="294" name="t_data_tracer">
      <Comment>各种单据操作记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="317" parent="294" name="t_department">
      <Comment>部门</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="318" parent="294" name="t_dict_key">
      <Comment>字段key</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="319" parent="294" name="t_dict_value">
      <Comment>字典的值</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="320" parent="294" name="t_employee">
      <Comment>员工表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="321" parent="294" name="t_feedback">
      <Comment>意见反馈</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="322" parent="294" name="t_file">
      <Comment>文件</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="323" parent="294" name="t_goods">
      <Comment>商品</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="324" parent="294" name="t_heart_beat_record">
      <Comment>公用服务 - 服务心跳</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="325" parent="294" name="t_help_doc">
      <Comment>帮助文档</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="326" parent="294" name="t_help_doc_catalog">
      <Comment>帮助文档-目录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="327" parent="294" name="t_help_doc_relation">
      <Comment>帮助文档-关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="328" parent="294" name="t_help_doc_view_record">
      <Comment>帮助文档-查看记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="329" parent="294" name="t_interface_config">
      <Comment>接口配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="330" parent="294" name="t_interface_header">
      <Comment>接口请求头表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="331" parent="294" name="t_interface_param_mapping">
      <Comment>接口参数映射表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="332" parent="294" name="t_interface_task">
      <Comment>接口任务表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="333" parent="294" name="t_interface_task_log">
      <Comment>接口任务执行日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="334" parent="294" name="t_login_fail">
      <Comment>登录失败次数记录表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="335" parent="294" name="t_login_log">
      <Comment>用户登录日志</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="336" parent="294" name="t_mail_template">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="337" parent="294" name="t_menu">
      <Comment>菜单表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="338" parent="294" name="t_message">
      <Comment>通知消息</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="339" parent="294" name="t_notice">
      <Comment>通知</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="340" parent="294" name="t_notice_type">
      <Comment>通知类型</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="341" parent="294" name="t_notice_view_record">
      <Comment>通知查看记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="342" parent="294" name="t_notice_visible_range">
      <Comment>通知可见范围</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="343" parent="294" name="t_oa_bank">
      <Comment>OA银行信息
</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="344" parent="294" name="t_oa_enterprise">
      <Comment>OA企业模块&#xd;
</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="345" parent="294" name="t_oa_enterprise_employee">
      <Comment>企业关联的员工</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="346" parent="294" name="t_oa_invoice">
      <Comment>OA发票信息
</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="347" parent="294" name="t_operate_log">
      <Comment>操作记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="348" parent="294" name="t_password_log">
      <Comment>密码修改记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="349" parent="294" name="t_position">
      <Comment>职务表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="350" parent="294" name="t_push_batch">
      <Comment>推送批次表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="351" parent="294" name="t_push_config">
      <Comment>推送配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="352" parent="294" name="t_push_record">
      <Comment>推送记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="353" parent="294" name="t_reload_item">
      <Comment>reload项目</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="354" parent="294" name="t_reload_result">
      <Comment>reload结果</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="355" parent="294" name="t_role">
      <Comment>角色表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="356" parent="294" name="t_role_data_scope">
      <Comment>角色的数据范围</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="357" parent="294" name="t_role_employee">
      <Comment>角色员工功能表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="358" parent="294" name="t_role_menu">
      <Comment>角色-菜单
</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="359" parent="294" name="t_serial_number">
      <Comment>单号生成器定义表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="360" parent="294" name="t_serial_number_record">
      <Comment>serial_number记录表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="361" parent="294" name="t_smart_job">
      <Comment>定时任务配置 @listen</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="362" parent="294" name="t_smart_job_log">
      <Comment>定时任务-执行记录 @listen</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="363" parent="294" name="t_table_column">
      <Comment>表格的自定义列存储</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="364" parent="294" name="t_third_party_dict_type">
      <Comment>第三方字典类型表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="365" parent="294" name="t_third_party_dict_value">
      <Comment>第三方字典值表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="366" parent="294" name="t_third_party_system">
      <Comment>第三方系统表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="367" parent="308" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>数据源ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="368" parent="308" name="name">
      <Comment>数据源名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="369" parent="308" name="description">
      <Comment>数据源描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="370" parent="308" name="db_type">
      <Comment>数据库类型，如MySQL、Oracle等</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="371" parent="308" name="driver_id">
      <Comment>驱动ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="372" parent="308" name="driver_class_name">
      <Comment>驱动类名</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="373" parent="308" name="host">
      <Comment>主机地址</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="374" parent="308" name="port">
      <Comment>端口号</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="375" parent="308" name="database_name">
      <Comment>数据库名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="376" parent="308" name="url">
      <Comment>连接URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="377" parent="308" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="378" parent="308" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="379" parent="308" name="max_active">
      <Comment>最大连接数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>10</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="380" parent="308" name="initial_size">
      <Comment>初始连接数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>5</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="381" parent="308" name="min_idle">
      <Comment>最小空闲连接数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>5</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="382" parent="308" name="max_wait">
      <Comment>最大等待时间</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>60000</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="383" parent="308" name="connection_status">
      <Comment>连接状态：0-未连接，1-已连接</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="384" parent="308" name="is_default">
      <Comment>是否为默认数据源：0-否，1-是</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="385" parent="308" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="386" parent="308" name="create_user_id">
      <Comment>创建人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="387" parent="308" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>21</Position>
    </column>
    <column id="388" parent="308" name="update_user_id">
      <Comment>更新人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="389" parent="308" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>23</Position>
    </column>
    <index id="390" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="391" parent="308" name="uk_name">
      <ColNames>name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="392" parent="308" name="idx_db_type">
      <ColNames>db_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="393" parent="308" name="idx_driver_id">
      <ColNames>driver_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="394" parent="308" name="idx_is_default">
      <ColNames>is_default</ColNames>
      <Type>btree</Type>
    </index>
    <key id="395" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="396" parent="308" name="uk_name">
      <UnderlyingIndexName>uk_name</UnderlyingIndexName>
    </key>
    <column id="397" parent="309" name="id">
      <AutoIncrement>32</AutoIncrement>
      <Comment>驱动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="398" parent="309" name="db_type">
      <Comment>数据库类型，如MySQL、Oracle等</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="399" parent="309" name="driver_name">
      <Comment>驱动名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="400" parent="309" name="driver_class_name">
      <Comment>驱动类名</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="401" parent="309" name="driver_version">
      <Comment>驱动版本</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="402" parent="309" name="driver_file_name">
      <Comment>驱动文件名</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="403" parent="309" name="driver_file_path">
      <Comment>驱动文件路径</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="404" parent="309" name="description">
      <Comment>描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="405" parent="309" name="create_user_id">
      <Comment>创建人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="406" parent="309" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="407" parent="309" name="update_user_id">
      <Comment>更新人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="408" parent="309" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
    </column>
    <index id="409" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="410" parent="309" name="idx_db_type">
      <ColNames>db_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="411" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="412" parent="310" name="category_id">
      <AutoIncrement>381</AutoIncrement>
      <Comment>分类id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="413" parent="310" name="category_name">
      <Comment>分类名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="414" parent="310" name="category_type">
      <Comment>分类类型</Comment>
      <DasType>smallint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="415" parent="310" name="parent_id">
      <Comment>父级id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="416" parent="310" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="417" parent="310" name="disabled_flag">
      <Comment>是否禁用</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="418" parent="310" name="deleted_flag">
      <Comment>是否删除</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="419" parent="310" name="remark">
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="420" parent="310" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <column id="421" parent="310" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <index id="422" parent="310" name="PRIMARY">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="423" parent="310" name="idx_parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="424" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="425" parent="311" name="change_log_id">
      <AutoIncrement>19</AutoIncrement>
      <Comment>更新日志id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="426" parent="311" name="version">
      <Comment>版本</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="427" parent="311" name="type">
      <Comment>更新类型:[1:特大版本功能更新;2:功能更新;3:bug修复]</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="428" parent="311" name="publish_author">
      <Comment>发布人</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="429" parent="311" name="public_date">
      <Comment>发布日期</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="430" parent="311" name="content">
      <Comment>更新内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="431" parent="311" name="link">
      <Comment>跳转链接</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="432" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="433" parent="311" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="434" parent="311" name="PRIMARY">
      <ColNames>change_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="435" parent="311" name="version_unique">
      <ColNames>version</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="436" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="437" parent="311" name="version_unique">
      <UnderlyingIndexName>version_unique</UnderlyingIndexName>
    </key>
    <column id="438" parent="312" name="table_name">
      <Comment>表名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="439" parent="312" name="basic">
      <Comment>基础命名信息</Comment>
      <DasType>text|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="440" parent="312" name="fields">
      <Comment>字段列表</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="441" parent="312" name="insert_and_update">
      <Comment>新建、修改</Comment>
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="442" parent="312" name="delete_info">
      <Comment>删除</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="443" parent="312" name="query_fields">
      <Comment>查询</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="444" parent="312" name="table_fields">
      <Comment>列表</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="445" parent="312" name="detail">
      <Comment>详情</Comment>
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="446" parent="312" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="447" parent="312" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <index id="448" parent="312" name="PRIMARY">
      <ColNames>table_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="449" parent="312" name="table_unique">
      <ColNames>table_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="450" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="451" parent="312" name="table_unique">
      <UnderlyingIndexName>table_unique</UnderlyingIndexName>
    </key>
    <column id="452" parent="313" name="config_id">
      <AutoIncrement>10</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="453" parent="313" name="config_name">
      <Comment>参数名字</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="454" parent="313" name="config_key">
      <Comment>参数key</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="455" parent="313" name="config_value">
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="456" parent="313" name="remark">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="457" parent="313" name="update_time">
      <Comment>上次修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="458" parent="313" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="459" parent="313" name="PRIMARY">
      <ColNames>config_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="460" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="461" parent="314" name="id">
      <AutoIncrement>26</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="462" parent="314" name="system_code">
      <Comment>第三方系统编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="463" parent="314" name="dict_type_code">
      <Comment>字典类型编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="464" parent="314" name="dict_type_name">
      <Comment>字典类型名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="465" parent="314" name="source_value">
      <Comment>源数据值</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="466" parent="314" name="source_name">
      <Comment>源数据名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="467" parent="314" name="target_value">
      <Comment>目标数据值</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="468" parent="314" name="target_name">
      <Comment>目标数据名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="469" parent="314" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="470" parent="314" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="471" parent="314" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="472" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="473" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <index id="474" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="475" parent="314" name="uk_system_dict_source">
      <ColNames>system_code
dict_type_code
source_value
deleted_flag</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="476" parent="314" name="idx_system_code">
      <ColNames>system_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="477" parent="314" name="idx_dict_type_code">
      <ColNames>dict_type_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="478" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="479" parent="314" name="uk_system_dict_source">
      <UnderlyingIndexName>uk_system_dict_source</UnderlyingIndexName>
    </key>
    <column id="480" parent="315" name="id">
      <AutoIncrement>47</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="481" parent="315" name="system_code">
      <Comment>第三方系统编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="482" parent="315" name="dict_type_code">
      <Comment>字典类型编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="483" parent="315" name="dict_type_name">
      <Comment>字典类型名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="484" parent="315" name="description">
      <Comment>描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="485" parent="315" name="status">
      <Comment>状态 0停用 1启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="486" parent="315" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="487" parent="315" name="sort_order">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="488" parent="315" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="489" parent="315" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="490" parent="315" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <index id="491" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="492" parent="315" name="uk_system_dict_type">
      <ColNames>system_code
dict_type_code
deleted_flag</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="493" parent="315" name="idx_system_code">
      <ColNames>system_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="494" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="495" parent="315" name="uk_system_dict_type">
      <UnderlyingIndexName>uk_system_dict_type</UnderlyingIndexName>
    </key>
    <column id="496" parent="316" name="data_tracer_id">
      <AutoIncrement>189</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="497" parent="316" name="data_id">
      <Comment>各种单据的id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="498" parent="316" name="type">
      <Comment>单据类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="499" parent="316" name="content">
      <Comment>操作内容</Comment>
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="500" parent="316" name="diff_old">
      <Comment>差异：旧的数据</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="501" parent="316" name="diff_new">
      <Comment>差异：新的数据</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="502" parent="316" name="extra_data">
      <Comment>额外信息</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="503" parent="316" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="504" parent="316" name="user_type">
      <Comment>用户类型：1 后管用户 </Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="505" parent="316" name="user_name">
      <Comment>用户名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="506" parent="316" name="ip">
      <Comment>ip</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="507" parent="316" name="ip_region">
      <Comment>ip地区</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="508" parent="316" name="user_agent">
      <Comment>用户ua</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="509" parent="316" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
    </column>
    <column id="510" parent="316" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <index id="511" parent="316" name="PRIMARY">
      <ColNames>data_tracer_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="512" parent="316" name="order_id_order_type">
      <ColNames>data_id
type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="513" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="514" parent="317" name="department_id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>部门主键id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="515" parent="317" name="name">
      <Comment>部门名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="516" parent="317" name="manager_id">
      <Comment>部门负责人id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="517" parent="317" name="parent_id">
      <Comment>部门的父级id</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="518" parent="317" name="sort">
      <Comment>部门排序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="519" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="520" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="521" parent="317" name="PRIMARY">
      <ColNames>department_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="522" parent="317" name="parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="523" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="524" parent="318" name="dict_key_id">
      <AutoIncrement>4</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="525" parent="318" name="key_code">
      <Comment>编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="526" parent="318" name="key_name">
      <Comment>名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="527" parent="318" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="528" parent="318" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="529" parent="318" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="530" parent="318" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="531" parent="318" name="PRIMARY">
      <ColNames>dict_key_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="532" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="533" parent="319" name="dict_value_id">
      <AutoIncrement>6</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="534" parent="319" name="dict_key_id">
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="535" parent="319" name="value_code">
      <Comment>编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="536" parent="319" name="value_name">
      <Comment>名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="537" parent="319" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="538" parent="319" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="539" parent="319" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="540" parent="319" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <column id="541" parent="319" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="542" parent="319" name="PRIMARY">
      <ColNames>dict_value_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="543" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="544" parent="320" name="employee_id">
      <AutoIncrement>76</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="545" parent="320" name="login_name">
      <Comment>登录帐号</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="546" parent="320" name="login_pwd">
      <Comment>登录密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="547" parent="320" name="actual_name">
      <Comment>员工名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="548" parent="320" name="avatar">
      <DasType>varchar(200)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="549" parent="320" name="gender">
      <Comment>性别</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="550" parent="320" name="phone">
      <Comment>手机号码</Comment>
      <DasType>varchar(15)|0s</DasType>
      <Position>7</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="551" parent="320" name="department_id">
      <Comment>部门id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="552" parent="320" name="position_id">
      <Comment>职务ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="553" parent="320" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="554" parent="320" name="disabled_flag">
      <Comment>是否被禁用 0否1是</Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="555" parent="320" name="deleted_flag">
      <Comment>是否删除0否 1是</Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="556" parent="320" name="administrator_flag">
      <Comment>是否为超级管理员: 0 不是，1是</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="557" parent="320" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="558" parent="320" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
    </column>
    <column id="559" parent="320" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <index id="560" parent="320" name="PRIMARY">
      <ColNames>employee_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="561" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="562" parent="321" name="feedback_id">
      <AutoIncrement>14</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="563" parent="321" name="feedback_content">
      <Comment>反馈内容</Comment>
      <DasType>text|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="564" parent="321" name="feedback_attachment">
      <Comment>反馈图片</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="565" parent="321" name="user_id">
      <Comment>创建人id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="566" parent="321" name="user_type">
      <Comment>创建人用户类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="567" parent="321" name="user_name">
      <Comment>创建人姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="568" parent="321" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="569" parent="321" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="570" parent="321" name="PRIMARY">
      <ColNames>feedback_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="571" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="572" parent="322" name="file_id">
      <AutoIncrement>118</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="573" parent="322" name="folder_type">
      <Comment>文件夹类型</Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="574" parent="322" name="file_name">
      <Comment>文件名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="575" parent="322" name="file_size">
      <Comment>文件大小</Comment>
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="576" parent="322" name="file_key">
      <Comment>文件key，用于文件下载</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="577" parent="322" name="file_type">
      <Comment>文件类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="578" parent="322" name="creator_id">
      <Comment>创建人，即上传人</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="579" parent="322" name="creator_user_type">
      <Comment>创建人用户类型</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="580" parent="322" name="creator_name">
      <Comment>创建人姓名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="581" parent="322" name="update_time">
      <Comment>上次更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="582" parent="322" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <index id="583" parent="322" name="PRIMARY">
      <ColNames>file_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="584" parent="322" name="uk_file_key">
      <ColNames>file_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="585" parent="322" name="module_id_module_type">
      <ColNames>folder_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="586" parent="322" name="module_type">
      <ColNames>folder_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="587" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="588" parent="322" name="uk_file_key">
      <UnderlyingIndexName>uk_file_key</UnderlyingIndexName>
    </key>
    <column id="589" parent="323" name="goods_id">
      <AutoIncrement>34</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="590" parent="323" name="goods_status">
      <Comment>商品状态:[1:预约中,2:售卖中,3:售罄]</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="591" parent="323" name="category_id">
      <Comment>商品类目</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="592" parent="323" name="goods_name">
      <Comment>商品名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="593" parent="323" name="place">
      <Comment>产地</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="594" parent="323" name="price">
      <Comment>价格</Comment>
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="595" parent="323" name="shelves_flag">
      <Comment>上架状态</Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="596" parent="323" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="597" parent="323" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="598" parent="323" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <column id="599" parent="323" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
    </column>
    <index id="600" parent="323" name="PRIMARY">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="601" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="602" parent="324" name="heart_beat_record_id">
      <AutoIncrement>672</AutoIncrement>
      <Comment>自增id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="603" parent="324" name="project_path">
      <Comment>项目名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="604" parent="324" name="server_ip">
      <Comment>服务器ip</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="605" parent="324" name="process_no">
      <Comment>进程号</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="606" parent="324" name="process_start_time">
      <Comment>进程开启时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="607" parent="324" name="heart_beat_time">
      <Comment>心跳时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="608" parent="324" name="PRIMARY">
      <ColNames>heart_beat_record_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="609" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="610" parent="325" name="help_doc_id">
      <AutoIncrement>35</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="611" parent="325" name="help_doc_catalog_id">
      <Comment>类型1公告 2动态</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="612" parent="325" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="613" parent="325" name="content_text">
      <Comment>文本内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="614" parent="325" name="content_html">
      <Comment>html内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="615" parent="325" name="attachment">
      <Comment>附件</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="616" parent="325" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="617" parent="325" name="page_view_count">
      <Comment>页面浏览量，传说中的pv</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="618" parent="325" name="user_view_count">
      <Comment>用户浏览量，传说中的uv</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="619" parent="325" name="author">
      <Comment>作者</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="620" parent="325" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <column id="621" parent="325" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="622" parent="325" name="PRIMARY">
      <ColNames>help_doc_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="623" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="624" parent="326" name="help_doc_catalog_id">
      <AutoIncrement>12</AutoIncrement>
      <Comment>帮助文档目录</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="625" parent="326" name="name">
      <Comment>名称</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="626" parent="326" name="sort">
      <Comment>排序字段</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="627" parent="326" name="parent_id">
      <Comment>父级id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="628" parent="326" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="629" parent="326" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <index id="630" parent="326" name="PRIMARY">
      <ColNames>help_doc_catalog_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="631" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="632" parent="327" name="relation_id">
      <Comment>关联id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="633" parent="327" name="relation_name">
      <Comment>关联名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="634" parent="327" name="help_doc_id">
      <Comment>文档id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="635" parent="327" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="636" parent="327" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <index id="637" parent="327" name="PRIMARY">
      <ColNames>relation_id
help_doc_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="638" parent="327" name="uni_menu_help_doc">
      <ColNames>relation_id
help_doc_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="639" parent="327" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="640" parent="327" name="uni_menu_help_doc">
      <UnderlyingIndexName>uni_menu_help_doc</UnderlyingIndexName>
    </key>
    <column id="641" parent="328" name="help_doc_id">
      <Comment>通知公告id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="642" parent="328" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="643" parent="328" name="user_name">
      <Comment>用户名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="644" parent="328" name="page_view_count">
      <Comment>查看次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="645" parent="328" name="first_ip">
      <Comment>首次ip</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="646" parent="328" name="first_user_agent">
      <Comment>首次用户设备等标识</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="647" parent="328" name="last_ip">
      <Comment>最后一次ip</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="648" parent="328" name="last_user_agent">
      <Comment>最后一次用户设备等标识</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="649" parent="328" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="650" parent="328" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <index id="651" parent="328" name="PRIMARY">
      <ColNames>help_doc_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="652" parent="328" name="uk_notice_employee">
      <ColNames>help_doc_id
user_id</ColNames>
      <Comment>资讯员工</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="653" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="654" parent="328" name="uk_notice_employee">
      <UnderlyingIndexName>uk_notice_employee</UnderlyingIndexName>
    </key>
    <column id="655" parent="329" name="id">
      <AutoIncrement>1584</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="656" parent="329" name="system_code">
      <Comment>系统编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="657" parent="329" name="system_name">
      <Comment>系统名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="658" parent="329" name="interface_name">
      <Comment>接口名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="659" parent="329" name="interface_type">
      <Comment>接口类型：REST、WEBSERVICE、CUSTOM</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="660" parent="329" name="request_url">
      <Comment>请求URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="661" parent="329" name="request_method">
      <Comment>请求方法：GET、POST、PUT、DELETE、PATCH</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="662" parent="329" name="web_service_operation">
      <Comment>WebService操作名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="663" parent="329" name="namespace">
      <Comment>命名空间</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="664" parent="329" name="operation">
      <Comment>操作</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="665" parent="329" name="param_mapping_id">
      <Comment>参数映射ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="666" parent="329" name="request_body_type">
      <Comment>请求体类型：JSON、FORM、XML、NONE</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;JSON&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="667" parent="329" name="request_body_template">
      <Comment>请求体模板</Comment>
      <DasType>text|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="668" parent="329" name="enable_encryption">
      <Comment>是否启用加密 0否 1是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="669" parent="329" name="encryption_algorithm">
      <Comment>加密算法</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="670" parent="329" name="encryption_key">
      <Comment>加密密钥</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="671" parent="329" name="encryption_mode">
      <Comment>加密模式</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="672" parent="329" name="encryption_iv">
      <Comment>初始向量(IV)</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="673" parent="329" name="encryption_padding">
      <Comment>填充方式</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="674" parent="329" name="enable_signature">
      <Comment>是否启用签名 0否 1是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="675" parent="329" name="signature_algorithm">
      <Comment>签名算法</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="676" parent="329" name="signature_key">
      <Comment>签名密钥</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="677" parent="329" name="signature_mode">
      <Comment>签名方式</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="678" parent="329" name="encrypt_type">
      <Comment>加密类型：SERVICE(使用加密服务)、CLASS_METHOD(使用类方法)、BUILTIN(内置加密)</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="679" parent="329" name="encrypt_service_name">
      <Comment>加密服务名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>25</Position>
    </column>
    <column id="680" parent="329" name="encrypt_class_name">
      <Comment>加密类名</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>26</Position>
    </column>
    <column id="681" parent="329" name="encrypt_method_name">
      <Comment>加密方法名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>27</Position>
    </column>
    <column id="682" parent="329" name="decrypt_method_name">
      <Comment>解密方法名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>28</Position>
    </column>
    <column id="683" parent="329" name="encrypt_service_url">
      <Comment>第三方加密服务URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>29</Position>
    </column>
    <column id="684" parent="329" name="encrypt_config">
      <Comment>加密配置参数(JSON格式)</Comment>
      <DasType>text|0s</DasType>
      <Position>30</Position>
    </column>
    <column id="685" parent="329" name="http_encrypt_data_param">
      <Comment>HTTP加密服务数据参数名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>31</Position>
    </column>
    <column id="686" parent="329" name="http_encrypt_result_path">
      <Comment>HTTP加密服务结果路径</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>32</Position>
    </column>
    <column id="687" parent="329" name="http_signature_result_path">
      <Comment>HTTP签名结果路径</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>33</Position>
    </column>
    <column id="688" parent="329" name="http_access_token_path">
      <Comment>HTTP访问令牌路径</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>34</Position>
    </column>
    <column id="689" parent="329" name="http_extra_params">
      <Comment>HTTP加密服务额外参数(JSON格式)</Comment>
      <DasType>text|0s</DasType>
      <Position>35</Position>
    </column>
    <column id="690" parent="329" name="http_headers">
      <Comment>HTTP加密服务请求头(JSON格式)</Comment>
      <DasType>text|0s</DasType>
      <Position>36</Position>
    </column>
    <column id="691" parent="329" name="http_timeout">
      <Comment>HTTP请求超时时间(毫秒)</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>30000</DefaultExpression>
      <Position>37</Position>
    </column>
    <column id="692" parent="329" name="http_retry_count">
      <Comment>HTTP请求重试次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>3</DefaultExpression>
      <Position>38</Position>
    </column>
    <column id="693" parent="329" name="data_source_id">
      <Comment>数据源ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>39</Position>
    </column>
    <column id="694" parent="329" name="table_name">
      <Comment>表名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
    </column>
    <column id="695" parent="329" name="sql_query">
      <Comment>SQL查询语句</Comment>
      <DasType>text|0s</DasType>
      <Position>41</Position>
    </column>
    <column id="696" parent="329" name="max_rows">
      <Comment>批处理最大行数</Comment>
      <DasType>int|0s</DasType>
      <Position>42</Position>
    </column>
    <column id="697" parent="329" name="status">
      <Comment>状态 0停用 1启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>43</Position>
    </column>
    <column id="698" parent="329" name="response_processor_class">
      <Comment>动态结果处理类</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>44</Position>
    </column>
    <column id="699" parent="329" name="response_parse_config">
      <Comment>响应解析配置(JSON格式)</Comment>
      <DasType>text|0s</DasType>
      <Position>45</Position>
    </column>
    <column id="700" parent="329" name="enable_response_parse">
      <Comment>是否启用响应解析: 0-否，1-是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>46</Position>
    </column>
    <column id="701" parent="329" name="description">
      <Comment>描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>47</Position>
    </column>
    <column id="702" parent="329" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>48</Position>
    </column>
    <column id="703" parent="329" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>49</Position>
    </column>
    <column id="704" parent="329" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>50</Position>
    </column>
    <column id="705" parent="329" name="input_data_template">
      <Comment>入参数据模板</Comment>
      <DasType>text|0s</DasType>
      <Position>51</Position>
    </column>
    <index id="706" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="707" parent="329" name="idx_system_code">
      <ColNames>system_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="708" parent="329" name="idx_param_mapping_id">
      <ColNames>param_mapping_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="709" parent="329" name="idx_encrypt_type">
      <ColNames>encrypt_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="710" parent="329" name="idx_encrypt_service_name">
      <ColNames>encrypt_service_name</ColNames>
      <Type>btree</Type>
    </index>
    <index id="711" parent="329" name="idx_encrypt_service_url">
      <ColNames>encrypt_service_url</ColNames>
      <Type>btree</Type>
    </index>
    <index id="712" parent="329" name="idx_http_encrypt_data_param">
      <ColNames>http_encrypt_data_param</ColNames>
      <Type>btree</Type>
    </index>
    <index id="713" parent="329" name="idx_enable_response_parse">
      <ColNames>enable_response_parse</ColNames>
      <Type>btree</Type>
    </index>
    <key id="714" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="715" parent="330" name="id">
      <AutoIncrement>1744162025866</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="716" parent="330" name="interface_id">
      <Comment>接口配置ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="717" parent="330" name="name">
      <Comment>请求头名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="718" parent="330" name="value">
      <Comment>请求头值</Comment>
      <DasType>varchar(500)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="719" parent="330" name="description">
      <Comment>描述</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="720" parent="330" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="721" parent="330" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="722" parent="330" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="723" parent="330" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="724" parent="330" name="idx_interface_id">
      <ColNames>interface_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="725" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="726" parent="331" name="id">
      <AutoIncrement>1744126860732</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="727" parent="331" name="interface_id">
      <Comment>接口配置ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="728" parent="331" name="param_name">
      <Comment>源参数名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="729" parent="331" name="target_param_name">
      <Comment>目标参数名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="730" parent="331" name="data_type">
      <Comment>数据类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="731" parent="331" name="default_value">
      <Comment>默认值</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="732" parent="331" name="dict_type">
      <Comment>字典类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="733" parent="331" name="required">
      <Comment>是否必填 0否 1是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="734" parent="331" name="filter_null_value">
      <Comment>是否过滤空值和null 0否 1是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="735" parent="331" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="736" parent="331" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="737" parent="331" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="738" parent="331" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="739" parent="331" name="idx_interface_id">
      <ColNames>interface_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="740" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="741" parent="332" name="id">
      <AutoIncrement>480</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="742" parent="332" name="task_name">
      <Comment>任务名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="743" parent="332" name="task_description">
      <Comment>任务描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="744" parent="332" name="interface_id">
      <Comment>关联的接口配置ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="745" parent="332" name="interface_name">
      <Comment>接口名称（冗余）</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="746" parent="332" name="system_code">
      <Comment>系统编码（冗余）</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="747" parent="332" name="data_sql">
      <Comment>数据获取SQL</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="748" parent="332" name="trigger_type">
      <Comment>触发类型：CRON表达式 或 FIXED_DELAY</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="749" parent="332" name="trigger_value">
      <Comment>触发值：CRON表达式 或 固定间隔（秒）</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="750" parent="332" name="enabled_flag">
      <Comment>是否启用 0禁用 1启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="751" parent="332" name="last_execute_time">
      <Comment>最后一次执行时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="752" parent="332" name="last_execute_status">
      <Comment>最后一次执行状态：0-失败，1-成功</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="753" parent="332" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="754" parent="332" name="create_user_id">
      <Comment>创建人ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="755" parent="332" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="756" parent="332" name="update_user_id">
      <Comment>更新人ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="757" parent="332" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <index id="758" parent="332" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="759" parent="332" name="idx_interface_id">
      <ColNames>interface_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="760" parent="332" name="idx_enabled_flag">
      <ColNames>enabled_flag</ColNames>
      <Type>btree</Type>
    </index>
    <index id="761" parent="332" name="idx_deleted_flag">
      <ColNames>deleted_flag</ColNames>
      <Type>btree</Type>
    </index>
    <key id="762" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="763" parent="333" name="id">
      <AutoIncrement>71</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="764" parent="333" name="task_id">
      <Comment>任务ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="765" parent="333" name="task_name">
      <Comment>任务名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="766" parent="333" name="success">
      <Comment>是否成功 0失败 1成功</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="767" parent="333" name="execute_result">
      <Comment>执行结果</Comment>
      <DasType>longtext|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="768" parent="333" name="request_body">
      <Comment>请求体</Comment>
      <DasType>longtext|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="769" parent="333" name="response_body">
      <Comment>响应体</Comment>
      <DasType>longtext|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="770" parent="333" name="status_code">
      <Comment>HTTP状态码</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="771" parent="333" name="execute_start_time">
      <Comment>执行开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="772" parent="333" name="execute_end_time">
      <Comment>执行结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="773" parent="333" name="execute_time_millis">
      <Comment>执行时长（毫秒）</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="774" parent="333" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="775" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="776" parent="333" name="idx_task_id">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="777" parent="333" name="idx_success">
      <ColNames>success</ColNames>
      <Type>btree</Type>
    </index>
    <index id="778" parent="333" name="idx_execute_start_time">
      <ColNames>execute_start_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="779" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="780" parent="334" name="login_fail_id">
      <AutoIncrement>85</AutoIncrement>
      <Comment>自增id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="781" parent="334" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="782" parent="334" name="user_type">
      <Comment>用户类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="783" parent="334" name="login_name">
      <Comment>登录名</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="784" parent="334" name="login_fail_count">
      <Comment>连续登录失败次数</Comment>
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="785" parent="334" name="lock_flag">
      <Comment>锁定状态:1锁定，0未锁定</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="786" parent="334" name="login_lock_begin_time">
      <Comment>连续登录失败锁定开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="787" parent="334" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="788" parent="334" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="789" parent="334" name="PRIMARY">
      <ColNames>login_fail_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="790" parent="334" name="uid_and_utype">
      <ColNames>user_id
user_type</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="791" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="792" parent="334" name="uid_and_utype">
      <UnderlyingIndexName>uid_and_utype</UnderlyingIndexName>
    </key>
    <column id="793" parent="335" name="login_log_id">
      <AutoIncrement>2094</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="794" parent="335" name="user_id">
      <Comment>用户id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="795" parent="335" name="user_type">
      <Comment>用户类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="796" parent="335" name="user_name">
      <Comment>用户名</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="797" parent="335" name="login_ip">
      <Comment>用户ip</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="798" parent="335" name="login_ip_region">
      <Comment>用户ip地区</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="799" parent="335" name="user_agent">
      <Comment>user-agent信息</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="800" parent="335" name="login_result">
      <Comment>登录结果：0成功 1失败 2 退出</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="801" parent="335" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="802" parent="335" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <column id="803" parent="335" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <index id="804" parent="335" name="PRIMARY">
      <ColNames>login_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="805" parent="335" name="customer_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="806" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="807" parent="336" name="template_code">
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="808" parent="336" name="template_subject">
      <Comment>模板名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="809" parent="336" name="template_content">
      <Comment>模板内容</Comment>
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="810" parent="336" name="template_type">
      <Comment>解析类型 string，freemarker</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="811" parent="336" name="disable_flag">
      <Comment>是否禁用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="812" parent="336" name="update_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="813" parent="336" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="814" parent="336" name="PRIMARY">
      <ColNames>template_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="815" parent="336" name="template_code">
      <ColNames>template_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="816" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="817" parent="336" name="template_code">
      <UnderlyingIndexName>template_code</UnderlyingIndexName>
    </key>
    <column id="818" parent="337" name="menu_id">
      <AutoIncrement>274</AutoIncrement>
      <Comment>菜单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="819" parent="337" name="menu_name">
      <Comment>菜单名称</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="820" parent="337" name="menu_type">
      <Comment>类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="821" parent="337" name="parent_id">
      <Comment>父菜单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="822" parent="337" name="sort">
      <Comment>显示顺序</Comment>
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="823" parent="337" name="path">
      <Comment>路由地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="824" parent="337" name="component">
      <Comment>组件路径</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="825" parent="337" name="perms_type">
      <Comment>权限类型</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="826" parent="337" name="api_perms">
      <Comment>后端权限字符串</Comment>
      <DasType>text|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="827" parent="337" name="web_perms">
      <Comment>前端权限字符串</Comment>
      <DasType>text|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="828" parent="337" name="icon">
      <Comment>菜单图标</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="829" parent="337" name="context_menu_id">
      <Comment>功能点关联菜单ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="830" parent="337" name="frame_flag">
      <Comment>是否为外链</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="831" parent="337" name="frame_url">
      <Comment>外链地址</Comment>
      <DasType>text|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="832" parent="337" name="cache_flag">
      <Comment>是否缓存</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="833" parent="337" name="visible_flag">
      <Comment>显示状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="834" parent="337" name="disabled_flag">
      <Comment>禁用状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="835" parent="337" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="836" parent="337" name="create_user_id">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="837" parent="337" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="838" parent="337" name="update_user_id">
      <Comment>更新人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="839" parent="337" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>22</Position>
    </column>
    <index id="840" parent="337" name="PRIMARY">
      <ColNames>menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="841" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="842" parent="338" name="message_id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>消息id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="843" parent="338" name="message_type">
      <Comment>消息类型</Comment>
      <DasType>smallint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="844" parent="338" name="receiver_user_type">
      <Comment>接收者用户类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="845" parent="338" name="receiver_user_id">
      <Comment>接收者用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="846" parent="338" name="data_id">
      <Comment>相关数据id</Comment>
      <DasType>varchar(500)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="847" parent="338" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="848" parent="338" name="content">
      <Comment>内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="849" parent="338" name="read_flag">
      <Comment>是否已读</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="850" parent="338" name="read_time">
      <Comment>已读时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="851" parent="338" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="852" parent="338" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <index id="853" parent="338" name="PRIMARY">
      <ColNames>message_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="854" parent="338" name="idx_msg">
      <ColNames>message_type
receiver_user_type
receiver_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="855" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="856" parent="339" name="notice_id">
      <AutoIncrement>177</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="857" parent="339" name="notice_type_id">
      <Comment>类型1公告 2动态</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="858" parent="339" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="859" parent="339" name="all_visible_flag">
      <Comment>是否全部可见</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="860" parent="339" name="scheduled_publish_flag">
      <Comment>是否定时发布</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="861" parent="339" name="publish_time">
      <Comment>发布时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="862" parent="339" name="content_text">
      <Comment>文本内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="863" parent="339" name="content_html">
      <Comment>html内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="864" parent="339" name="attachment">
      <Comment>附件</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="865" parent="339" name="page_view_count">
      <Comment>页面浏览量，传说中的pv</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="866" parent="339" name="user_view_count">
      <Comment>用户浏览量，传说中的uv</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="867" parent="339" name="source">
      <Comment>来源</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="868" parent="339" name="author">
      <Comment>作者</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="869" parent="339" name="document_number">
      <Comment>文号，如：1024创新实验室发〔2022〕字第36号</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="870" parent="339" name="deleted_flag">
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="871" parent="339" name="create_user_id">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="872" parent="339" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>17</Position>
    </column>
    <column id="873" parent="339" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <index id="874" parent="339" name="PRIMARY">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="875" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="876" parent="340" name="notice_type_id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>通知类型</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="877" parent="340" name="notice_type_name">
      <Comment>类型名称</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="878" parent="340" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="879" parent="340" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>4</Position>
    </column>
    <index id="880" parent="340" name="PRIMARY">
      <ColNames>notice_type_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="881" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="882" parent="341" name="notice_id">
      <Comment>通知公告id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="883" parent="341" name="employee_id">
      <Comment>员工id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="884" parent="341" name="page_view_count">
      <Comment>查看次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="885" parent="341" name="first_ip">
      <Comment>首次ip</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="886" parent="341" name="first_user_agent">
      <Comment>首次用户设备等标识</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="887" parent="341" name="last_ip">
      <Comment>最后一次ip</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="888" parent="341" name="last_user_agent">
      <Comment>最后一次用户设备等标识</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="889" parent="341" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="890" parent="341" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="891" parent="341" name="PRIMARY">
      <ColNames>notice_id
employee_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="892" parent="341" name="uk_notice_employee">
      <ColNames>notice_id
employee_id</ColNames>
      <Comment>资讯员工</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="893" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="894" parent="341" name="uk_notice_employee">
      <UnderlyingIndexName>uk_notice_employee</UnderlyingIndexName>
    </key>
    <column id="895" parent="342" name="notice_id">
      <Comment>资讯id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="896" parent="342" name="data_type">
      <Comment>数据类型1员工 2部门</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="897" parent="342" name="data_id">
      <Comment>员工or部门id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="898" parent="342" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="899" parent="342" name="uk_notice_data">
      <ColNames>notice_id
data_type
data_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="900" parent="342" name="uk_notice_data">
      <UnderlyingIndexName>uk_notice_data</UnderlyingIndexName>
    </key>
    <column id="901" parent="343" name="bank_id">
      <AutoIncrement>29</AutoIncrement>
      <Comment>银行信息ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="902" parent="343" name="bank_name">
      <Comment>开户银行</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="903" parent="343" name="account_name">
      <Comment>账户名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="904" parent="343" name="account_number">
      <Comment>账号</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="905" parent="343" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="906" parent="343" name="business_flag">
      <Comment>是否对公</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="907" parent="343" name="enterprise_id">
      <Comment>企业ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="908" parent="343" name="disabled_flag">
      <Comment>禁用状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="909" parent="343" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="910" parent="343" name="create_user_id">
      <Comment>创建人ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="911" parent="343" name="create_user_name">
      <Comment>创建人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="912" parent="343" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="913" parent="343" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
    </column>
    <index id="914" parent="343" name="PRIMARY">
      <ColNames>bank_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="915" parent="343" name="idx_enterprise_id">
      <ColNames>enterprise_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="916" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="917" parent="344" name="enterprise_id">
      <AutoIncrement>127</AutoIncrement>
      <Comment>企业ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="918" parent="344" name="enterprise_name">
      <Comment>企业名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="919" parent="344" name="enterprise_logo">
      <Comment>企业logo</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="920" parent="344" name="type">
      <Comment>类型（1:有限公司;2:合伙公司）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="921" parent="344" name="unified_social_credit_code">
      <Comment>统一社会信用代码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="922" parent="344" name="contact">
      <Comment>联系人</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="923" parent="344" name="contact_phone">
      <Comment>联系人电话</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="924" parent="344" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="925" parent="344" name="province">
      <Comment>省份</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="926" parent="344" name="province_name">
      <Comment>省份名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="927" parent="344" name="city">
      <Comment>市</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="928" parent="344" name="city_name">
      <Comment>城市名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="929" parent="344" name="district">
      <Comment>区县</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="930" parent="344" name="district_name">
      <Comment>区县名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="931" parent="344" name="address">
      <Comment>详细地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="932" parent="344" name="business_license">
      <Comment>营业执照</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="933" parent="344" name="disabled_flag">
      <Comment>禁用状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="934" parent="344" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="935" parent="344" name="create_user_id">
      <Comment>创建人ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="936" parent="344" name="create_user_name">
      <Comment>创建人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="937" parent="344" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="938" parent="344" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>22</Position>
    </column>
    <index id="939" parent="344" name="PRIMARY">
      <ColNames>enterprise_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="940" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="941" parent="345" name="enterprise_employee_id">
      <AutoIncrement>159</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="942" parent="345" name="enterprise_id">
      <Comment>订单ID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="943" parent="345" name="employee_id">
      <Comment>货物名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="944" parent="345" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>4</Position>
    </column>
    <column id="945" parent="345" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="946" parent="345" name="PRIMARY">
      <ColNames>enterprise_employee_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="947" parent="345" name="uk_enterprise_employee">
      <ColNames>enterprise_id
employee_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="948" parent="345" name="idx_enterprise_id">
      <ColNames>enterprise_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="949" parent="345" name="idx_employee_id">
      <ColNames>employee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="950" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="951" parent="345" name="uk_enterprise_employee">
      <UnderlyingIndexName>uk_enterprise_employee</UnderlyingIndexName>
    </key>
    <column id="952" parent="346" name="invoice_id">
      <AutoIncrement>17</AutoIncrement>
      <Comment>发票信息ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="953" parent="346" name="invoice_heads">
      <Comment>开票抬头</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="954" parent="346" name="taxpayer_identification_number">
      <Comment>纳税人识别号</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="955" parent="346" name="account_number">
      <Comment>银行账户</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="956" parent="346" name="bank_name">
      <Comment>开户行</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="957" parent="346" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="958" parent="346" name="enterprise_id">
      <Comment>企业ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="959" parent="346" name="disabled_flag">
      <Comment>禁用状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="960" parent="346" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="961" parent="346" name="create_user_id">
      <Comment>创建人ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="962" parent="346" name="create_user_name">
      <Comment>创建人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="963" parent="346" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="964" parent="346" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
    </column>
    <index id="965" parent="346" name="PRIMARY">
      <ColNames>invoice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="966" parent="346" name="idx_enterprise_id">
      <ColNames>enterprise_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="967" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="968" parent="347" name="operate_log_id">
      <AutoIncrement>6132</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="969" parent="347" name="operate_user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="970" parent="347" name="operate_user_type">
      <Comment>用户类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="971" parent="347" name="operate_user_name">
      <Comment>用户名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="972" parent="347" name="module">
      <Comment>操作模块</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="973" parent="347" name="content">
      <Comment>操作内容</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="974" parent="347" name="url">
      <Comment>请求路径</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="975" parent="347" name="method">
      <Comment>请求方法</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="976" parent="347" name="param">
      <Comment>请求参数</Comment>
      <DasType>text|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="977" parent="347" name="ip">
      <Comment>请求ip</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="978" parent="347" name="ip_region">
      <Comment>请求ip地区</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="979" parent="347" name="user_agent">
      <Comment>请求user-agent</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="980" parent="347" name="success_flag">
      <Comment>请求结果 0失败 1成功</Comment>
      <DasType>tinyint|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="981" parent="347" name="fail_reason">
      <Comment>失败原因</Comment>
      <DasType>longtext|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="982" parent="347" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
    </column>
    <column id="983" parent="347" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <index id="984" parent="347" name="PRIMARY">
      <ColNames>operate_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="985" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="986" parent="348" name="id">
      <AutoIncrement>13</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="987" parent="348" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="988" parent="348" name="user_type">
      <Comment>用户类型</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="989" parent="348" name="old_password">
      <Comment>旧密码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="990" parent="348" name="new_password">
      <Comment>新密码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="991" parent="348" name="update_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="992" parent="348" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="993" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="994" parent="348" name="user_and_type_index">
      <ColNames>user_id
user_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="995" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="996" parent="349" name="position_id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>职务ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="997" parent="349" name="position_name">
      <Comment>职务名称</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="998" parent="349" name="level">
      <Comment>职级</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="999" parent="349" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1000" parent="349" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1001" parent="349" name="deleted_flag">
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1002" parent="349" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1003" parent="349" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="1004" parent="349" name="PRIMARY">
      <ColNames>position_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1005" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1006" parent="350" name="id">
      <AutoIncrement>515</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1007" parent="350" name="task_id">
      <Comment>任务ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1008" parent="350" name="batch_no">
      <Comment>批次号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1009" parent="350" name="total_count">
      <Comment>总记录数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1010" parent="350" name="success_count">
      <Comment>成功数量</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1011" parent="350" name="failed_count">
      <Comment>失败数量</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1012" parent="350" name="batch_status">
      <Comment>批次状态：0-进行中，1-已完成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1013" parent="350" name="current_page">
      <Comment>当前处理页码</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1014" parent="350" name="total_pages">
      <Comment>总页数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1015" parent="350" name="time_window_start">
      <Comment>时间窗口开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="1016" parent="350" name="time_window_end">
      <Comment>时间窗口结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1017" parent="350" name="progress_info">
      <Comment>进度信息（JSON格式）</Comment>
      <DasType>json|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="1018" parent="350" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="1019" parent="350" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1020" parent="350" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="1021" parent="350" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>16</Position>
    </column>
    <index id="1022" parent="350" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1023" parent="350" name="uk_batch_no">
      <ColNames>batch_no</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1024" parent="350" name="idx_task_status">
      <ColNames>task_id
batch_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1025" parent="350" name="idx_push_batch_task">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1026" parent="350" name="idx_task_id">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1027" parent="350" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1028" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1029" parent="350" name="uk_batch_no">
      <UnderlyingIndexName>uk_batch_no</UnderlyingIndexName>
    </key>
    <column id="1030" parent="351" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1031" parent="351" name="task_id">
      <Comment>任务ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1032" parent="351" name="data_id_field">
      <Comment>数据唯一标识字段名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;id&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1033" parent="351" name="target_data_id_field">
      <Comment>目标数据唯一标识字段名（查询结果中的别名）</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1034" parent="351" name="enable_deduplication">
      <Comment>是否启用去重：0-否，1-是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1035" parent="351" name="enable_data_change_detection">
      <Comment>是否启用数据变更检测：0-否，1-是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1036" parent="351" name="max_retry_count">
      <Comment>最大重试次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>3</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1037" parent="351" name="batch_size">
      <Comment>批次大小</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>100</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1038" parent="351" name="page_size">
      <Comment>分页查询每页大小</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1000</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1039" parent="351" name="datetime_format">
      <Comment>日期时间格式（用于时间占位符）</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;yyyy-MM-dd HH:mm:ss&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1040" parent="351" name="date_format">
      <Comment>日期格式（用于时间占位符）</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;yyyy-MM-dd&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="1041" parent="351" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1042" parent="351" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
    </column>
    <column id="1043" parent="351" name="start_time">
      <Comment>推送起始时间（可选，设置后只推送此时间之后的数据）</Comment>
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1044" parent="351" name="update_time_field">
      <Comment>数据更新时间字段名（用于排序和断点续传）</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;update_time&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="1045" parent="351" name="time_window_hours">
      <Comment>时间窗口小时数，用于分段处理大数据量</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>24</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="1046" parent="351" name="current_window_start">
      <Comment>当前处理窗口开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="1047" parent="351" name="current_window_end">
      <Comment>当前处理窗口结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>18</Position>
    </column>
    <index id="1048" parent="351" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1049" parent="351" name="uk_task_id">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1050" parent="351" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1051" parent="351" name="uk_task_id">
      <UnderlyingIndexName>uk_task_id</UnderlyingIndexName>
    </key>
    <column id="1052" parent="352" name="id">
      <AutoIncrement>43</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1053" parent="352" name="task_id">
      <Comment>任务ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1054" parent="352" name="batch_no">
      <Comment>批次号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1055" parent="352" name="data_id">
      <Comment>数据唯一标识</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1056" parent="352" name="data_hash">
      <Comment>数据内容哈希值</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1057" parent="352" name="push_status">
      <Comment>推送状态：0-待推送，1-推送成功，2-推送失败</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1058" parent="352" name="retry_count">
      <Comment>重试次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1059" parent="352" name="error_message">
      <Comment>错误信息</Comment>
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1060" parent="352" name="push_time">
      <Comment>推送时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1061" parent="352" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1062" parent="352" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <index id="1063" parent="352" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1064" parent="352" name="uk_task_data">
      <ColNames>task_id
data_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1065" parent="352" name="idx_push_record_task_status">
      <ColNames>task_id
push_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1066" parent="352" name="idx_task_status">
      <ColNames>task_id
push_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1067" parent="352" name="idx_batch_no">
      <ColNames>batch_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1068" parent="352" name="idx_push_record_batch">
      <ColNames>batch_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1069" parent="352" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1070" parent="352" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1071" parent="352" name="uk_task_data">
      <UnderlyingIndexName>uk_task_data</UnderlyingIndexName>
    </key>
    <column id="1072" parent="353" name="tag">
      <Comment>项名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="1073" parent="353" name="args">
      <Comment>参数 可选</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1074" parent="353" name="identification">
      <Comment>运行标识</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="1075" parent="353" name="update_time">
      <DasType>datetime|0s</DasType>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>4</Position>
    </column>
    <column id="1076" parent="353" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="1077" parent="353" name="PRIMARY">
      <ColNames>tag</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1078" parent="353" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1079" parent="354" name="tag">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1080" parent="354" name="identification">
      <Comment>运行标识</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1081" parent="354" name="args">
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1082" parent="354" name="result">
      <Comment>是否成功 </Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1083" parent="354" name="exception">
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1084" parent="354" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1085" parent="355" name="role_id">
      <AutoIncrement>60</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1086" parent="355" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1087" parent="355" name="role_code">
      <Comment>角色编码</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1088" parent="355" name="remark">
      <Comment>角色描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1089" parent="355" name="update_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <column id="1090" parent="355" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="1091" parent="355" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1092" parent="355" name="role_code_uni">
      <ColNames>role_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1093" parent="355" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1094" parent="355" name="role_code_uni">
      <UnderlyingIndexName>role_code_uni</UnderlyingIndexName>
    </key>
    <column id="1095" parent="356" name="id">
      <AutoIncrement>69</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1096" parent="356" name="data_scope_type">
      <Comment>数据范围id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1097" parent="356" name="view_type">
      <Comment>数据范围类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1098" parent="356" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1099" parent="356" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <column id="1100" parent="356" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="1101" parent="356" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1102" parent="356" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1103" parent="357" name="id">
      <AutoIncrement>343</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1104" parent="357" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1105" parent="357" name="employee_id">
      <Comment>员工id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1106" parent="357" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>4</Position>
    </column>
    <column id="1107" parent="357" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="1108" parent="357" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1109" parent="357" name="uk_role_employee">
      <ColNames>role_id
employee_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1110" parent="357" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1111" parent="357" name="uk_role_employee">
      <UnderlyingIndexName>uk_role_employee</UnderlyingIndexName>
    </key>
    <column id="1112" parent="358" name="role_menu_id">
      <AutoIncrement>838</AutoIncrement>
      <Comment>主键id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1113" parent="358" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1114" parent="358" name="menu_id">
      <Comment>菜单id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1115" parent="358" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>4</Position>
    </column>
    <column id="1116" parent="358" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="1117" parent="358" name="PRIMARY">
      <ColNames>role_menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1118" parent="358" name="idx_role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1119" parent="358" name="idx_menu_id">
      <ColNames>menu_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1120" parent="358" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1121" parent="359" name="serial_number_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1122" parent="359" name="business_name">
      <Comment>业务名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1123" parent="359" name="format">
      <Comment>格式[yyyy]表示年,[mm]标识月,[dd]表示日,[nnn]表示三位数字</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1124" parent="359" name="rule_type">
      <Comment>规则格式。none没有周期, year 年周期, month月周期, day日周期</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1125" parent="359" name="init_number">
      <Comment>初始值</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1126" parent="359" name="step_random_range">
      <Comment>步长随机数</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1127" parent="359" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1128" parent="359" name="last_number">
      <Comment>上次产生的单号, 默认为空</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1129" parent="359" name="last_time">
      <Comment>上次产生的单号时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1130" parent="359" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <column id="1131" parent="359" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
    </column>
    <index id="1132" parent="359" name="PRIMARY">
      <ColNames>serial_number_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1133" parent="359" name="key_name">
      <ColNames>business_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1134" parent="359" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1135" parent="359" name="key_name">
      <UnderlyingIndexName>key_name</UnderlyingIndexName>
    </key>
    <column id="1136" parent="360" name="serial_number_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1137" parent="360" name="record_date">
      <Comment>记录日期</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1138" parent="360" name="last_number">
      <Comment>最后更新值</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1139" parent="360" name="last_time">
      <Comment>最后更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1140" parent="360" name="count">
      <Comment>更新次数</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1141" parent="360" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="1142" parent="360" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="1143" parent="360" name="uk_generator">
      <ColNames>serial_number_id
record_date</ColNames>
      <Type>btree</Type>
    </index>
    <column id="1144" parent="361" name="job_id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>任务id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1145" parent="361" name="job_name">
      <Comment>任务名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1146" parent="361" name="job_class">
      <Comment>任务执行类</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1147" parent="361" name="trigger_type">
      <Comment>触发类型</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1148" parent="361" name="trigger_value">
      <Comment>触发配置</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1149" parent="361" name="enabled_flag">
      <Comment>是否开启</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1150" parent="361" name="param">
      <Comment>参数</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1151" parent="361" name="last_execute_time">
      <Comment>最后一次执行时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1152" parent="361" name="last_execute_log_id">
      <Comment>最后一次执行记录id</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1153" parent="361" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1154" parent="361" name="remark">
      <Comment>描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1155" parent="361" name="deleted_flag">
      <Comment>删除状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="1156" parent="361" name="update_name">
      <Comment>更新人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="1157" parent="361" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="1158" parent="361" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
    </column>
    <index id="1159" parent="361" name="PRIMARY">
      <ColNames>job_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1160" parent="361" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1161" parent="362" name="log_id">
      <AutoIncrement>8186</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1162" parent="362" name="job_id">
      <Comment>任务id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1163" parent="362" name="job_name">
      <Comment>任务名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1164" parent="362" name="param">
      <Comment>执行参数</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1165" parent="362" name="success_flag">
      <Comment>是否成功</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1166" parent="362" name="execute_start_time">
      <Comment>执行开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1167" parent="362" name="execute_time_millis">
      <Comment>执行时长</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1168" parent="362" name="execute_end_time">
      <Comment>执行结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1169" parent="362" name="execute_result">
      <DasType>varchar(2000)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1170" parent="362" name="ip">
      <Comment>ip</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1171" parent="362" name="process_id">
      <Comment>进程id</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1172" parent="362" name="program_path">
      <Comment>程序目录</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="1173" parent="362" name="create_name">
      <Comment>创建人</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="1174" parent="362" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <index id="1175" parent="362" name="PRIMARY">
      <ColNames>log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1176" parent="362" name="idx_job_id">
      <ColNames>job_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1177" parent="362" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1178" parent="363" name="table_column_id">
      <AutoIncrement>10</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1179" parent="363" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1180" parent="363" name="user_type">
      <Comment>用户类型</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1181" parent="363" name="table_id">
      <Comment>表格id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1182" parent="363" name="columns">
      <Comment>具体的表格列，存入的json</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1183" parent="363" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1184" parent="363" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
    </column>
    <index id="1185" parent="363" name="PRIMARY">
      <ColNames>table_column_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1186" parent="363" name="uni_employee_table">
      <ColNames>user_id
table_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1187" parent="363" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1188" parent="363" name="uni_employee_table">
      <UnderlyingIndexName>uni_employee_table</UnderlyingIndexName>
    </key>
    <column id="1189" parent="364" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1190" parent="364" name="system_code">
      <Comment>系统编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1191" parent="364" name="dict_code">
      <Comment>字典编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1192" parent="364" name="dict_name">
      <Comment>字典名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1193" parent="364" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1194" parent="364" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1195" parent="364" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1196" parent="364" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="1197" parent="364" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1198" parent="364" name="uk_system_dict">
      <ColNames>system_code
dict_code
deleted_flag</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1199" parent="364" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1200" parent="364" name="uk_system_dict">
      <UnderlyingIndexName>uk_system_dict</UnderlyingIndexName>
    </key>
    <column id="1201" parent="365" name="id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1202" parent="365" name="system_code">
      <Comment>系统编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1203" parent="365" name="dict_type_id">
      <Comment>字典类型ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1204" parent="365" name="dict_value">
      <Comment>字典值</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1205" parent="365" name="dict_name">
      <Comment>字典名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1206" parent="365" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1207" parent="365" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1208" parent="365" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1209" parent="365" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="1210" parent="365" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <index id="1211" parent="365" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1212" parent="365" name="uk_type_value">
      <ColNames>dict_type_id
dict_value
deleted_flag</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1213" parent="365" name="idx_system_code">
      <ColNames>system_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1214" parent="365" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1215" parent="365" name="uk_type_value">
      <UnderlyingIndexName>uk_type_value</UnderlyingIndexName>
    </key>
    <column id="1216" parent="366" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1217" parent="366" name="system_code">
      <Comment>系统编码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1218" parent="366" name="system_name">
      <Comment>系统名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1219" parent="366" name="system_desc">
      <Comment>系统描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1220" parent="366" name="base_url">
      <Comment>系统基础URL</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1221" parent="366" name="contact_person">
      <Comment>联系人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1222" parent="366" name="contact_phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1223" parent="366" name="status">
      <Comment>状态 0停用 1启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1224" parent="366" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1225" parent="366" name="deleted_flag">
      <Comment>删除标识 0未删除 1已删除</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1226" parent="366" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1227" parent="366" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="1228" parent="366" name="sort_order">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <index id="1229" parent="366" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1230" parent="366" name="uk_system_code">
      <ColNames>system_code
deleted_flag</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1231" parent="366" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1232" parent="366" name="uk_system_code">
      <UnderlyingIndexName>uk_system_code</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>