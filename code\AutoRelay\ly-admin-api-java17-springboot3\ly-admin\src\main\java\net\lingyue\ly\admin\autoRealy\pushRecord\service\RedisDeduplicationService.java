package net.lingyue.ly.admin.autoRealy.pushRecord.service;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.base.module.support.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

import java.util.*;

/**
 * Redis去重服务 - 高性能去重实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class RedisDeduplicationService {

    @Resource
    private RedisService redisService;

    // Redis键前缀
    private static final String DEDUP_KEY_PREFIX = "push:dedup:";
    private static final String HASH_KEY_PREFIX = "push:hash:";
    private static final String RETRY_KEY_PREFIX = "push:retry:";
    private static final String MAX_ID_KEY_PREFIX = "push:maxid:";
    private static final String LAST_TIME_KEY_PREFIX = "push:lasttime:";
    private static final String TIME_WINDOW_KEY_PREFIX = "push:window:";
    
    // 默认过期时间：7天
    private static final int DEFAULT_EXPIRE_SECONDS = 7 * 24 * 3600;

    /**
     * 批量检查数据是否已推送
     *
     * @param taskId 任务ID
     * @param dataIds 数据ID列表
     * @return 已推送的数据ID集合
     */
    public Set<String> batchCheckPushed(Long taskId, List<String> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return new HashSet<>();
        }

        // 使用MGET批量获取值，非空即视为存在
        List<String> keys = new ArrayList<>(dataIds.size());
        for (String dataId : dataIds) {
            keys.add(generateRedisKey(DEDUP_KEY_PREFIX, taskId + ":" + dataId));
        }
        List<String> values = redisService.multiGet(keys);

        Set<String> result = new HashSet<>();
        for (int i = 0; i < dataIds.size(); i++) {
            if (values != null && i < values.size() && values.get(i) != null) {
                result.add(dataIds.get(i));
            }
        }
        return result;
    }

    /**
     * 批量获取数据哈希值
     *
     * @param taskId 任务ID
     * @param dataIds 数据ID列表
     * @return 数据ID到哈希值的映射
     */
    public Map<String, String> batchGetDataHashes(Long taskId, List<String> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return new HashMap<>();
        }

        List<String> keys = new ArrayList<>(dataIds.size());
        for (String dataId : dataIds) {
            keys.add(generateRedisKey(HASH_KEY_PREFIX, taskId + ":" + dataId));
        }
        List<String> values = redisService.multiGet(keys);

        Map<String, String> result = new HashMap<>();
        for (int i = 0; i < dataIds.size(); i++) {
            String val = (values != null && i < values.size()) ? values.get(i) : null;
            if (val != null) {
                result.put(dataIds.get(i), val);
            }
        }
        return result;
    }

    /**
     * 批量获取重试次数
     *
     * @param taskId 任务ID
     * @param dataIds 数据ID列表
     * @return 数据ID到重试次数的映射
     */
    public Map<String, Integer> batchGetRetryCounts(Long taskId, List<String> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return new HashMap<>();
        }

        List<String> keys = new ArrayList<>(dataIds.size());
        for (String dataId : dataIds) {
            keys.add(generateRedisKey(RETRY_KEY_PREFIX, taskId + ":" + dataId));
        }
        List<String> values = redisService.multiGet(keys);

        Map<String, Integer> result = new HashMap<>();
        for (int i = 0; i < dataIds.size(); i++) {
            String retryValue = (values != null && i < values.size()) ? values.get(i) : null;
            if (retryValue != null) {
                try {
                    result.put(dataIds.get(i), Integer.parseInt(retryValue));
                } catch (NumberFormatException e) {
                    log.warn("解析重试次数失败: {}", retryValue);
                }
            }
        }
        return result;
    }

    /**
     * 批量标记数据为已推送（成功）
     *
     * @param taskId 任务ID
     * @param dataHashMap 数据ID到哈希值的映射
     */
    public void batchMarkAsPushed(Long taskId, Map<String, String> dataHashMap) {
        if (dataHashMap == null || dataHashMap.isEmpty()) {
            return;
        }

        Map<String, String> dedupKvs = new HashMap<>(dataHashMap.size());
        Map<String, String> hashKvs = new HashMap<>(dataHashMap.size());
        for (Map.Entry<String, String> entry : dataHashMap.entrySet()) {
            String dataId = entry.getKey();
            String dataHash = entry.getValue();
            dedupKvs.put(generateRedisKey(DEDUP_KEY_PREFIX, taskId + ":" + dataId), "1");
            hashKvs.put(generateRedisKey(HASH_KEY_PREFIX, taskId + ":" + dataId), dataHash);
        }

        // 使用管道批量写入并设置过期
        redisService.pipelineSetWithExpire(dedupKvs, DEFAULT_EXPIRE_SECONDS);
        redisService.pipelineSetWithExpire(hashKvs, DEFAULT_EXPIRE_SECONDS);

        log.debug("任务[{}]批量标记{}条数据为已推送", taskId, dataHashMap.size());
    }

    /**
     * 批量增加重试次数
     *
     * @param taskId 任务ID
     * @param dataIds 失败的数据ID列表
     */
    public void batchIncrementRetryCount(Long taskId, List<String> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return;
        }

        Map<String, Long> deltas = new HashMap<>(dataIds.size());
        for (String dataId : dataIds) {
            String key = generateRedisKey(RETRY_KEY_PREFIX, taskId + ":" + dataId);
            deltas.put(key, 1L);
        }
        // 管道原子自增并设置过期
        redisService.pipelineIncrByWithExpire(deltas, DEFAULT_EXPIRE_SECONDS);
        log.debug("任务[{}]批量增加{}条数据的重试次数", taskId, dataIds.size());
    }

    /**
     * 批量移除失败数据的Redis标记
     * 用于清理推送失败数据的预标记，确保Redis中只保留真正成功的数据
     *
     * @param taskId 任务ID
     * @param dataIds 失败的数据ID列表
     */
    public void batchRemoveFailedData(Long taskId, List<String> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return;
        }

        List<String> toDelete = new ArrayList<>(dataIds.size() * 2);
        for (String dataId : dataIds) {
            toDelete.add(generateRedisKey(DEDUP_KEY_PREFIX, taskId + ":" + dataId));
            toDelete.add(generateRedisKey(HASH_KEY_PREFIX, taskId + ":" + dataId));
        }
        redisService.delete(toDelete);

        log.info("任务[{}]批量移除{}条失败数据的Redis标记", taskId, dataIds.size());
    }

    /**
     * 保存/更新任务的最大处理ID
     *
     * @param taskId 任务ID
     * @param maxId 最大ID
     */
    public void saveMaxProcessedId(Long taskId, String maxId) {
        if (maxId == null || maxId.isEmpty()) {
            return;
        }

        String key = generateRedisKey(MAX_ID_KEY_PREFIX, taskId.toString());
        redisService.set(key, maxId, DEFAULT_EXPIRE_SECONDS);
        log.debug("任务[{}]保存最大处理ID: {}", taskId, maxId);
    }

    /**
     * 获取任务的最大处理ID
     *
     * @param taskId 任务ID
     * @return 最大处理ID，如果不存在返回"0"
     */
    public String getMaxProcessedId(Long taskId) {
        String key = generateRedisKey(MAX_ID_KEY_PREFIX, taskId.toString());
        String maxId = redisService.get(key);
        return maxId != null ? maxId : "0";
    }

    /**
     * 保存/更新任务的最后处理时间
     *
     * @param taskId 任务ID
     * @param lastTime 最后处理时间
     */
    public void saveLastProcessedTime(Long taskId, String lastTime) {
        if (lastTime == null || lastTime.isEmpty()) {
            return;
        }

        String key = generateRedisKey(LAST_TIME_KEY_PREFIX, taskId.toString());
        redisService.set(key, lastTime, DEFAULT_EXPIRE_SECONDS);
        log.debug("任务[{}]保存最后处理时间: {}", taskId, lastTime);
    }

    /**
     * 保存/更新任务的时间窗口信息
     *
     * 🔥 新增方法：保存推送周期的完整状态信息
     * 包括：窗口开始时间、结束时间、是否完成等
     *
     * @param taskId 任务ID
     * @param windowInfoJson 时间窗口信息（JSON格式）
     */
    public void saveTimeWindowInfo(Long taskId, String windowInfoJson) {
        if (windowInfoJson == null || windowInfoJson.isEmpty()) {
            return;
        }

        String key = generateRedisKey(TIME_WINDOW_KEY_PREFIX, taskId.toString());
        redisService.set(key, windowInfoJson, DEFAULT_EXPIRE_SECONDS);
        log.debug("任务[{}]保存时间窗口信息: {}", taskId, windowInfoJson);
    }

    /**
     * 获取任务的时间窗口信息
     *
     * @param taskId 任务ID
     * @return 时间窗口信息（JSON格式），如果不存在返回null
     */
    public String getTimeWindowInfo(Long taskId) {
        String key = generateRedisKey(TIME_WINDOW_KEY_PREFIX, taskId.toString());
        String windowInfo = redisService.get(key);
        log.debug("任务[{}]获取时间窗口信息: {}", taskId, windowInfo);
        return windowInfo;
    }

    /**
     * 获取任务的最后处理时间
     *
     * @param taskId 任务ID
     * @return 最后处理时间，如果不存在返回null
     */
    public String getLastProcessedTime(Long taskId) {
        String key = generateRedisKey(LAST_TIME_KEY_PREFIX, taskId.toString());
        return redisService.get(key);
    }

    /**
     * 清理任务相关的所有Redis数据
     *
     * @param taskId 任务ID
     */
    public void cleanupTaskData(Long taskId) {
        String dedupKey = generateRedisKey(DEDUP_KEY_PREFIX, taskId.toString());
        String hashKey = generateRedisKey(HASH_KEY_PREFIX, taskId.toString());
        String retryKey = generateRedisKey(RETRY_KEY_PREFIX, taskId.toString());
        String maxIdKey = generateRedisKey(MAX_ID_KEY_PREFIX, taskId.toString());

        redisService.delete(dedupKey);
        redisService.delete(hashKey);
        redisService.delete(retryKey);
        redisService.delete(maxIdKey);

        log.info("任务[{}]Redis数据清理完成", taskId);
    }

    /**
     * 计算数据哈希值
     *
     * @param data 数据
     * @return MD5哈希值
     */
    public String calculateDataHash(Map<String, Object> data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 使用无中间字符串的流式更新，按键排序，递归处理集合类型
            updateDigestWithObject(md, data);
            byte[] digest = md.digest();
            return bytesToHex(digest);
        } catch (Exception e) {
            // 兜底回退
            log.warn("计算数据哈希失败，使用数据字符串哈希", e);
            return DigestUtils.md5DigestAsHex(String.valueOf(data).getBytes(StandardCharsets.UTF_8));
        }
    }

    private void updateDigestWithObject(MessageDigest md, Object value) {
        if (value == null) {
            md.update((byte) 'n'); // null 标记
            return;
        }

        if (value instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) value;
            // 对键排序以保证稳定
            List<String> keys = new ArrayList<>(map.keySet());
            Collections.sort(keys);
            md.update((byte) '{');
            for (int i = 0; i < keys.size(); i++) {
                String k = keys.get(i);
                md.update((byte) '"');
                md.update(k.getBytes(StandardCharsets.UTF_8));
                md.update((byte) '"');
                md.update((byte) ':');
                updateDigestWithObject(md, map.get(k));
                if (i < keys.size() - 1) {
                    md.update((byte) ',');
                }
            }
            md.update((byte) '}');
            return;
        }

        if (value instanceof List) {
            List<?> list = (List<?>) value;
            md.update((byte) '[');
            for (int i = 0; i < list.size(); i++) {
                updateDigestWithObject(md, list.get(i));
                if (i < list.size() - 1) {
                    md.update((byte) ',');
                }
            }
            md.update((byte) ']');
            return;
        }

        // 基本类型统一转为UTF-8字节，增加类型标记避免数值与字符串冲突
        if (value instanceof Number) {
            md.update((byte) 'd');
            md.update(String.valueOf(value).getBytes(StandardCharsets.UTF_8));
        } else if (value instanceof Boolean) {
            md.update((byte) 'b');
            md.update(((Boolean) value) ? (byte) '1' : (byte) '0');
        } else if (value instanceof CharSequence) {
            md.update((byte) 's');
            md.update(String.valueOf(value).getBytes(StandardCharsets.UTF_8));
        } else {
            // 其他对象，使用toString的稳定性通常足够；如需更强一致性，可根据业务扩展
            md.update((byte) 'o');
            md.update(String.valueOf(value).getBytes(StandardCharsets.UTF_8));
        }
    }

    private String bytesToHex(byte[] bytes) {
        char[] hexArray = "0123456789abcdef".toCharArray();
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 生成Redis键
     */
    private String generateRedisKey(String prefix, String suffix) {
        return redisService.generateRedisKey(prefix, suffix);
    }

    /**
     * 获取任务去重统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息
     */
    public Map<String, Object> getTaskStats(Long taskId) {
        Map<String, Object> stats = new HashMap<>();

        String maxIdKey = generateRedisKey(MAX_ID_KEY_PREFIX, taskId.toString());

        // 由于使用了简化的Redis存储方式，这里只提供基本统计
        // 实际的统计可以通过扫描Redis键来实现，但为了性能考虑，这里提供估算值
        stats.put("pushedCount", "使用简化存储，无法直接统计");
        stats.put("hashCount", "使用简化存储，无法直接统计");
        stats.put("retryCount", "使用简化存储，无法直接统计");

        // 获取最大处理ID
        String maxId = redisService.get(maxIdKey);
        stats.put("maxProcessedId", maxId != null ? maxId : "0");

        return stats;
    }
}
