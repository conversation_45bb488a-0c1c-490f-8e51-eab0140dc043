<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-241.18034.62">
    <data-source name="阿里云" uuid="05477873-a91d-4b23-a77e-58ddde119a3a">
      <database-info product="MySQL" version="8.0.36" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.36" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>lingyuelabs</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="lingyue-admin" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="cs.lingyuelabs.cn" uuid="00482042-0fda-4596-827f-f9316ca18934">
      <database-info product="Oracle" version="Oracle Database 11g Enterprise Edition Release 11.2.0.1.0 - 64bit Production&#10;With the Partitioning, OLAP, Data Mining and Real Application Testing options" jdbc-version="11.2" driver-name="Oracle JDBC driver" driver-version="11.2.0.4.0" dbms="ORACLE" exact-version="11.2.0.1.0" exact-driver-version="11.2">
        <extra-name-characters>$#</extra-name-characters>
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="upper" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>zltbxt</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="PUBLIC" />
          </node>
        </introspection-scope>
      </schema-mapping>
      <load-sources>user_and_system_sources</load-sources>
    </data-source>
  </component>
</project>