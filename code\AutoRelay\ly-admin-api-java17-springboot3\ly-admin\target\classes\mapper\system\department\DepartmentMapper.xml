<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.module.system.department.dao.DepartmentDao">


    <select id="listAll" resultType="net.lingyue.ly.admin.module.system.department.domain.vo.DepartmentVO">
        SELECT t_department.*,
        t_employee.actual_name as managerName,
        parent_department.`name` as parentName
        FROM t_department
        left join t_employee on t_department.manager_id = t_employee.employee_id
        left join t_department parent_department on t_department.parent_id = parent_department.department_id
        order by sort desc
    </select>

    <select id="countSubDepartment" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_department
        WHERE parent_id = #{departmentId}
    </select>


</mapper>
