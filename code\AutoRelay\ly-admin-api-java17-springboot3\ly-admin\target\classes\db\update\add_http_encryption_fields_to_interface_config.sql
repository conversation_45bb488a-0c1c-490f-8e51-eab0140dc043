-- 为接口配置表添加HTTP加密服务所需的字段
-- 创建时间：2024-12-28
-- 说明：添加HTTP加密服务配置所需的字段

-- 添加HTTP加密服务字段
ALTER TABLE `t_interface_config`
ADD COLUMN `http_encrypt_data_param` varchar(50) DEFAULT NULL COMMENT 'HTTP加密服务数据参数名' AFTER `encrypt_config`,
ADD COLUMN `http_encrypt_result_path` varchar(200) DEFAULT NULL COMMENT 'HTTP加密服务结果路径' AFTER `http_encrypt_data_param`,
ADD COLUMN `http_signature_result_path` varchar(200) DEFAULT NULL COMMENT 'HTTP签名结果路径' AFTER `http_encrypt_result_path`,
ADD COLUMN `http_access_token_path` varchar(200) DEFAULT NULL COMMENT 'HTTP访问令牌路径' AFTER `http_signature_result_path`,
ADD COLUMN `http_extra_params` text DEFAULT NULL COMMENT 'HTTP加密服务额外参数(JSON格式)' AFTER `http_access_token_path`,
ADD COLUMN `http_headers` text DEFAULT NULL COMMENT 'HTTP加密服务请求头(JSON格式)' AFTER `http_extra_params`,
ADD COLUMN `http_timeout` int DEFAULT 30000 COMMENT 'HTTP请求超时时间(毫秒)' AFTER `http_headers`,
ADD COLUMN `http_retry_count` int DEFAULT 3 COMMENT 'HTTP请求重试次数' AFTER `http_timeout`;

-- 添加索引
CREATE INDEX idx_encrypt_service_url ON `t_interface_config`(`encrypt_service_url`);
CREATE INDEX idx_http_encrypt_data_param ON `t_interface_config`(`http_encrypt_data_param`); 