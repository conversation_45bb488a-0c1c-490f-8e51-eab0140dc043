-- 字典映射管理菜单配置（主从表模式）
-- 在此页面内完成字典类型和字典映射的管理
-- 
-- @Author:    贺哥
-- @Date:      2024-12-28
-- @Copyright  <a href="https://lingyuelabs.cn">凌跃</a>

-- 1. 删除原有的字典类型管理菜单（如果存在）
UPDATE t_menu 
SET deleted_flag = 1, update_time = NOW() 
WHERE menu_name = '字典类型管理' AND deleted_flag = 0;

-- 2. 更新数据字典映射管理菜单
UPDATE t_menu 
SET menu_name = '字典映射管理',
    path = '/auto-relay/data-dict-mapping/list',
    component = '/views/auto-relay/data-dict-mapping/data-dict-mapping-list.vue',
    update_time = NOW()
WHERE menu_name = '数据字典映射管理' AND deleted_flag = 0;

-- 3. 确保数据字典类型表存在并包含sortOrder字段
ALTER TABLE t_data_dict_type 
ADD COLUMN IF NOT EXISTS sort_order INT(11) DEFAULT 0 COMMENT '排序' 
AFTER remark;

-- 4. 创建一些示例字典类型数据（如果数据字典类型表为空）
INSERT IGNORE INTO t_data_dict_type (
    system_code, dict_type_code, dict_type_name, description, 
    status, sort_order, remark, deleted_flag, create_time, update_time
) VALUES 
('THIRD_PARTY_SYSTEM', 'CERTIFICATE_TYPE', '证件类型', '身份证、护照等证件类型字典', 1, 1, '用于映射不同系统的证件类型编码', 0, NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'GENDER', '性别', '性别字典', 1, 2, '用于映射不同系统的性别编码', 0, NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'EDUCATION', '学历', '学历字典', 1, 3, '用于映射不同系统的学历编码', 0, NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'MARITAL_STATUS', '婚姻状态', '婚姻状态字典', 1, 4, '用于映射不同系统的婚姻状态编码', 0, NOW(), NOW()),
('THIRD_PARTY_SYSTEM', 'NATIONALITY', '民族', '民族字典', 1, 5, '用于映射不同系统的民族编码', 0, NOW(), NOW());

-- 5. 清理不需要的菜单项
UPDATE t_menu 
SET deleted_flag = 1, update_time = NOW() 
WHERE parent_id IN (
    SELECT id FROM (
        SELECT id FROM t_menu WHERE menu_name = '字典类型管理' AND deleted_flag = 1
    ) temp
) AND deleted_flag = 0;

-- 6. 验证菜单结构
SELECT 
    m1.menu_name as '菜单名称',
    m1.path as '路径',
    m1.component as '组件',
    CASE WHEN m1.deleted_flag = 0 THEN '正常' ELSE '已删除' END as '状态'
FROM t_menu m1
WHERE (m1.menu_name LIKE '%字典%映射%' OR m1.menu_name LIKE '%字典%类型%')
ORDER BY m1.sort; 