-- 为接口配置表添加响应解析配置字段
-- 创建时间：2024-07-06
-- 说明：添加可配置的响应解析功能所需字段

-- 添加响应解析配置字段
ALTER TABLE `t_interface_config`
ADD COLUMN `response_parse_config` text DEFAULT NULL COMMENT '响应解析配置(JSON格式)' AFTER `response_processor_class`,
ADD COLUMN `enable_response_parse` tinyint(1) DEFAULT '0' COMMENT '是否启用响应解析: 0-否，1-是' AFTER `response_parse_config`;

-- 添加索引
CREATE INDEX idx_enable_response_parse ON `t_interface_config`(`enable_response_parse`); 