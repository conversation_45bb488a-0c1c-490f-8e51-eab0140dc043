-- 接口配置表加密和签名字段更新
-- 创建时间：2024-07-12

-- 添加加密相关字段
ALTER TABLE `t_interface_config` 
ADD COLUMN `enable_encryption` tinyint(1) DEFAULT '0' COMMENT '是否启用加密 0否 1是' AFTER `request_body_template`,
ADD COLUMN `encryption_algorithm` varchar(50) DEFAULT NULL COMMENT '加密算法' AFTER `enable_encryption`,
ADD COLUMN `encryption_key` varchar(200) DEFAULT NULL COMMENT '加密密钥' AFTER `encryption_algorithm`,
ADD COLUMN `encryption_mode` varchar(50) DEFAULT NULL COMMENT '加密模式' AFTER `encryption_key`,
ADD COLUMN `encryption_iv` varchar(100) DEFAULT NULL COMMENT '初始向量(IV)' AFTER `encryption_mode`,
ADD COLUMN `encryption_padding` varchar(50) DEFAULT NULL COMMENT '填充方式' AFTER `encryption_iv`;

-- 添加签名相关字段
ALTER TABLE `t_interface_config` 
ADD COLUMN `enable_signature` tinyint(1) DEFAULT '0' COMMENT '是否启用签名 0否 1是' AFTER `encryption_padding`,
ADD COLUMN `signature_algorithm` varchar(50) DEFAULT NULL COMMENT '签名算法' AFTER `enable_signature`,
ADD COLUMN `signature_key` varchar(200) DEFAULT NULL COMMENT '签名密钥' AFTER `signature_algorithm`,
ADD COLUMN `signature_mode` varchar(50) DEFAULT NULL COMMENT '签名方式' AFTER `signature_key`; 