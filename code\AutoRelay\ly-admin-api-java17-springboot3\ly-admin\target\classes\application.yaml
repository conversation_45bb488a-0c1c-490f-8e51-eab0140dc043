#############################################################################################################
#                                                                                                           #
# 为了减少重复配置，本配置文件为此ly-admin的独有配置，更多配置请查看 ly-base 项目中的 ly-base.yaml 通用配置文件。      #
# 其中此文件中配置可以覆盖 ly-base.yaml 中的通用配置，具体实现类请看类：YamlProcessor.java                          #
#                                                                                                           #
#############################################################################################################

# 项目配置: 名称、日志目录
project:
  name: ly-admin
  log-directory: ${localPath:/home}/logs/smart_admin_v3/ly-admin/${spring.profiles.active}

# 项目端口和url根路径
server:
  port: 18082
  servlet:
    context-path: /

# 环境
spring:
  profiles:
    active: 'dev'
