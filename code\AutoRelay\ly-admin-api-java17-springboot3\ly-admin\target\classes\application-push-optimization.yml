# 推送优化配置
push:
  optimization:
    # 是否启用Redis去重优化
    redis-deduplication-enabled: true
    
    # 是否只记录失败数据到数据库
    only-record-failures: true
    
    # Redis数据过期时间（秒）- 7天
    redis-expire-seconds: 604800
    
    # 批量操作大小
    batch-size: 1000
    
    # 是否启用性能监控
    performance-monitor-enabled: true
    
    # 是否启用降级机制（Redis故障时自动降级到数据库）
    fallback-to-db-enabled: true
    
    # Redis连接超时时间（毫秒）
    redis-timeout-ms: 3000
    
    # 最大重试次数
    max-retry-count: 3
    
    # 是否启用数据变更检测
    data-change-detection-enabled: true
    
    # 是否启用批量处理优化
    batch-processing-optimized: true

# Redis配置优化
spring:
  redis:
    # 连接池配置
    jedis:
      pool:
        # 最大连接数
        max-active: 100
        # 最大空闲连接数
        max-idle: 20
        # 最小空闲连接数
        min-idle: 5
        # 获取连接时的最大等待时间
        max-wait: 3000ms
    # 连接超时时间
    timeout: 3000ms
    # 命令超时时间
    command-timeout: 3000ms

# 日志配置
logging:
  level:
    # 推送相关日志级别
    net.lingyue.ly.admin.autoRealy.pushRecord: INFO
    # Redis去重服务日志
    net.lingyue.ly.admin.autoRealy.pushRecord.service.RedisDeduplicationService: DEBUG
    # 性能监控日志
    net.lingyue.ly.admin.autoRealy.pushRecord.service.PushPerformanceMonitorService: INFO

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,push-performance
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      # 可以配置监控数据导出到其他系统
      simple:
        enabled: true
