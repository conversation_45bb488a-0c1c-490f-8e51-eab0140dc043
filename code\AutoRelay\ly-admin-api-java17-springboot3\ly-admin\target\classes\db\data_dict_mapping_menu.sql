-- 数据字典映射管理菜单初始化脚本
-- 创建时间：2024-12-28
-- 用途：添加数据字典映射管理相关菜单

-- 1. 查找或创建"自动中继"父菜单
SET @auto_relay_parent_id = NULL;
SELECT menu_id INTO @auto_relay_parent_id 
FROM t_menu 
WHERE menu_name = '自动中继' AND deleted_flag = 0 
LIMIT 1;

-- 如果没有"自动中继"菜单，创建一个
INSERT INTO t_menu (menu_name, menu_type, parent_id, sort, icon, visible_flag, disabled_flag, deleted_flag, create_time, update_time)
SELECT '自动中继', 1, 0, 10, 'DatabaseOutlined', 1, 0, 0, NOW(), NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM t_menu WHERE menu_name = '自动中继' AND deleted_flag = 0
);

-- 重新获取父菜单ID
SELECT menu_id INTO @auto_relay_parent_id 
FROM t_menu 
WHERE menu_name = '自动中继' AND deleted_flag = 0 
LIMIT 1;

-- 2. 添加"数据字典映射管理"菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, sort, path, component, frame_flag, cache_flag, visible_flag, disabled_flag, perms_type, create_time, update_time)
VALUES ('数据字典映射管理', 2, @auto_relay_parent_id, 20, '/auto-relay/data-dict-mapping/list', '/auto-relay/data-dict-mapping/data-dict-mapping-list.vue', 0, 1, 1, 0, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    path = '/auto-relay/data-dict-mapping/list',
    component = '/auto-relay/data-dict-mapping/data-dict-mapping-list.vue',
    update_time = NOW();

-- 3. 获取刚创建的菜单ID作为功能点的父菜单
SET @dict_mapping_menu_id = NULL;
SELECT menu_id INTO @dict_mapping_menu_id 
FROM t_menu 
WHERE menu_name = '数据字典映射管理' AND deleted_flag = 0 
LIMIT 1;

-- 4. 添加功能点权限
INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms, perms_type, context_menu_id, create_time, update_time)
VALUES 
('查询', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:query', 1, @dict_mapping_menu_id, NOW(), NOW()),
('添加', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:add', 1, @dict_mapping_menu_id, NOW(), NOW()),
('更新', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:update', 1, @dict_mapping_menu_id, NOW(), NOW()),
('删除', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:delete', 1, @dict_mapping_menu_id, NOW(), NOW()),
('导出', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:export', 1, @dict_mapping_menu_id, NOW(), NOW()),
('导入', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:import', 1, @dict_mapping_menu_id, NOW(), NOW()),
('转换测试', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:convert', 1, @dict_mapping_menu_id, NOW(), NOW()),
('系统管理', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:systemManage', 1, @dict_mapping_menu_id, NOW(), NOW()),
('字典类型管理', 3, @dict_mapping_menu_id, 0, 1, 1, 0, 'dataDictMapping:dictTypeManage', 1, @dict_mapping_menu_id, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    api_perms = VALUES(api_perms),
    update_time = NOW();

-- 5. 删除旧的第三方字典管理菜单（如果存在）
UPDATE t_menu 
SET deleted_flag = 1, update_time = NOW() 
WHERE menu_name IN ('第三方字典管理', '第三方系统管理', '第三方字典类型管理', '第三方字典值管理') 
AND deleted_flag = 0;

COMMIT; 