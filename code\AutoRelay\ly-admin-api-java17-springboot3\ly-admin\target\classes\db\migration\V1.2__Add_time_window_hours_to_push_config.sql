-- 数据库迁移脚本：添加时间窗口小时字段
-- 版本：V1.2
-- 创建时间：2025-01-14
-- 说明：将时间窗口配置从分钟改为小时单位

-- 为推送配置表添加时间窗口小时字段
ALTER TABLE `t_push_config` ADD COLUMN `time_window_hours` int DEFAULT '1' COMMENT '时间窗口大小（小时），用于将大时间范围分割成小窗口进行处理，避免单次查询数据量过大';

-- 如果之前存在time_window_minutes字段，可以选择删除（可选）
-- ALTER TABLE `t_push_config` DROP COLUMN IF EXISTS `time_window_minutes`;
-- ALTER TABLE `t_push_config` DROP COLUMN IF EXISTS `max_time_window_minutes`;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 't_push_config' 
    AND COLUMN_NAME = 'time_window_hours';